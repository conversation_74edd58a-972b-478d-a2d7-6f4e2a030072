// Package ultrafast provides high-performance indexing with memory-mapped perfect hash tables
package main

import (
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"os"
	"sort"
	"syscall"
	"time"
	"unsafe"
)

// Record represents a single data record for indexing
type Record struct {
	LineNumber uint32
	Value      string
}

// RowRecord represents a complete row with all column values
type RowRecord struct {
	LineNumber uint32
	Values     map[string]string // column_name -> value
}

// FilterExpression represents a query filter with AND/OR logic
type FilterExpression struct {
	Type     FilterType
	Column   string
	Value    string
	Left     *FilterExpression
	Right    *FilterExpression
	Operator LogicalOperator
}

// FilterType represents the type of filter
type FilterType int

const (
	FilterTypeLeaf FilterType = iota // Single column=value condition
	FilterTypeAnd                    // AND operation
	FilterTypeOr                     // OR operation
)

// LogicalOperator represents logical operators
type LogicalOperator int

const (
	LogicalOperatorAnd LogicalOperator = iota
	LogicalOperatorOr
)

// QueryRequest represents a multi-column query request
type QueryRequest struct {
	Filter     *FilterExpression
	SelectCols []string // Columns to return in results
	Limit      int      // Maximum number of results (0 = no limit)
	Offset     int      // Number of results to skip
}

// QueryResult represents the result of a multi-column query
type QueryResult struct {
	Rows         []map[string]string // Each row as column_name -> value
	TotalMatches int                 // Total matches before limit/offset
	QueryTime    time.Duration       // Time taken to execute query
	Stats        QueryStats          // Detailed execution statistics
}

// QueryStats provides detailed query execution statistics
type QueryStats struct {
	IndexesUsed   []string // Which column indexes were accessed
	RowsScanned   int      // Number of rows examined
	IndexLookups  int      // Number of index lookups performed
	SetOperations int      // Number of AND/OR operations
	CacheHitRate  float64  // Cache hit rate across all indexes
	ExecutionPlan string   // Human-readable execution plan
}

// RoaringBitmap represents a compressed bitmap using Roaring bitmap algorithm
type RoaringBitmap struct {
	containers map[uint16]Container
	maxValue   uint32
}

// Container interface for different container types
type Container interface {
	Add(value uint16) Container
	Contains(value uint16) bool
	And(other Container) Container
	Or(other Container) Container
	ToSlice() []uint16
	Count() int
	IsFull() bool
}

// ArrayContainer stores values as sorted array (for sparse data)
type ArrayContainer struct {
	values []uint16
}

// BitmapContainer stores values as bitmap (for dense data)
type BitmapContainer struct {
	bitmap [1024]uint64 // 65536 bits / 64 = 1024 words
}

// NewRoaringBitmap creates a new RoaringBitmap
func NewRoaringBitmap() *RoaringBitmap {
	return &RoaringBitmap{
		containers: make(map[uint16]Container),
		maxValue:   0,
	}
}

// Add adds a value to the bitmap
func (rb *RoaringBitmap) Add(value uint32) {
	if value > rb.maxValue {
		rb.maxValue = value
	}

	high := uint16(value >> 16)
	low := uint16(value & 0xFFFF)

	container, exists := rb.containers[high]
	if !exists {
		container = &ArrayContainer{values: make([]uint16, 0, 4)}
	}

	rb.containers[high] = container.Add(low)
}

// Contains checks if a value exists in the bitmap
func (rb *RoaringBitmap) Contains(value uint32) bool {
	high := uint16(value >> 16)
	low := uint16(value & 0xFFFF)

	container, exists := rb.containers[high]
	if !exists {
		return false
	}

	return container.Contains(low)
}

// And performs intersection with another RoaringBitmap
func (rb *RoaringBitmap) And(other *RoaringBitmap) *RoaringBitmap {
	result := NewRoaringBitmap()

	for high, container := range rb.containers {
		if otherContainer, exists := other.containers[high]; exists {
			resultContainer := container.And(otherContainer)
			if resultContainer.Count() > 0 {
				result.containers[high] = resultContainer
			}
		}
	}

	return result
}

// Or performs union with another RoaringBitmap
func (rb *RoaringBitmap) Or(other *RoaringBitmap) *RoaringBitmap {
	result := NewRoaringBitmap()

	// Copy all containers from this bitmap
	for high, container := range rb.containers {
		result.containers[high] = container
	}

	// Merge containers from other bitmap
	for high, otherContainer := range other.containers {
		if container, exists := result.containers[high]; exists {
			result.containers[high] = container.Or(otherContainer)
		} else {
			result.containers[high] = otherContainer
		}
	}

	return result
}

// ToSlice returns all values as a sorted slice
func (rb *RoaringBitmap) ToSlice() []uint32 {
	var result []uint32

	// Get sorted keys
	var keys []uint16
	for high := range rb.containers {
		keys = append(keys, high)
	}

	// Sort keys for consistent output
	for i := 0; i < len(keys)-1; i++ {
		for j := i + 1; j < len(keys); j++ {
			if keys[i] > keys[j] {
				keys[i], keys[j] = keys[j], keys[i]
			}
		}
	}

	for _, high := range keys {
		container := rb.containers[high]
		values := container.ToSlice()
		for _, low := range values {
			result = append(result, (uint32(high)<<16)|uint32(low))
		}
	}

	return result
}

// Count returns the total number of set bits
func (rb *RoaringBitmap) Count() int {
	count := 0
	for _, container := range rb.containers {
		count += container.Count()
	}
	return count
}

// ArrayContainer implementation
func (ac *ArrayContainer) Add(value uint16) Container {
	// Check if value already exists
	for _, v := range ac.values {
		if v == value {
			return ac
		}
	}

	// Add value in sorted order
	newValues := make([]uint16, len(ac.values)+1)
	inserted := false
	j := 0

	for i, v := range ac.values {
		if !inserted && value < v {
			newValues[j] = value
			j++
			inserted = true
		}
		newValues[j] = ac.values[i]
		j++
	}

	if !inserted {
		newValues[len(newValues)-1] = value
	}

	// Convert to bitmap if too many values
	if len(newValues) > 4096 {
		bc := &BitmapContainer{}
		for _, v := range newValues {
			bc.Add(v)
		}
		return bc
	}

	return &ArrayContainer{values: newValues}
}

func (ac *ArrayContainer) Contains(value uint16) bool {
	for _, v := range ac.values {
		if v == value {
			return true
		}
		if v > value {
			break
		}
	}
	return false
}

func (ac *ArrayContainer) And(other Container) Container {
	if otherAc, ok := other.(*ArrayContainer); ok {
		result := &ArrayContainer{values: make([]uint16, 0)}
		i, j := 0, 0

		for i < len(ac.values) && j < len(otherAc.values) {
			if ac.values[i] == otherAc.values[j] {
				result.values = append(result.values, ac.values[i])
				i++
				j++
			} else if ac.values[i] < otherAc.values[j] {
				i++
			} else {
				j++
			}
		}

		return result
	}

	// Handle ArrayContainer AND BitmapContainer
	result := &ArrayContainer{values: make([]uint16, 0)}
	for _, v := range ac.values {
		if other.Contains(v) {
			result.values = append(result.values, v)
		}
	}
	return result
}

func (ac *ArrayContainer) Or(other Container) Container {
	if otherAc, ok := other.(*ArrayContainer); ok {
		result := &ArrayContainer{values: make([]uint16, 0, len(ac.values)+len(otherAc.values))}
		i, j := 0, 0

		for i < len(ac.values) && j < len(otherAc.values) {
			if ac.values[i] == otherAc.values[j] {
				result.values = append(result.values, ac.values[i])
				i++
				j++
			} else if ac.values[i] < otherAc.values[j] {
				result.values = append(result.values, ac.values[i])
				i++
			} else {
				result.values = append(result.values, otherAc.values[j])
				j++
			}
		}

		for i < len(ac.values) {
			result.values = append(result.values, ac.values[i])
			i++
		}

		for j < len(otherAc.values) {
			result.values = append(result.values, otherAc.values[j])
			j++
		}

		// Convert to bitmap if too many values
		if len(result.values) > 4096 {
			bc := &BitmapContainer{}
			for _, v := range result.values {
				bc.Add(v)
			}
			return bc
		}

		return result
	}

	// Handle ArrayContainer OR BitmapContainer
	bc := &BitmapContainer{}
	// Copy other bitmap
	if otherBc, ok := other.(*BitmapContainer); ok {
		bc.bitmap = otherBc.bitmap
	}
	// Add array values
	for _, v := range ac.values {
		bc.Add(v)
	}
	return bc
}

func (ac *ArrayContainer) ToSlice() []uint16 {
	result := make([]uint16, len(ac.values))
	copy(result, ac.values)
	return result
}

func (ac *ArrayContainer) Count() int {
	return len(ac.values)
}

func (ac *ArrayContainer) IsFull() bool {
	return len(ac.values) == 65536
}

// BitmapContainer implementation
func (bc *BitmapContainer) Add(value uint16) Container {
	wordIndex := value / 64
	bitIndex := value % 64
	bc.bitmap[wordIndex] |= 1 << bitIndex
	return bc
}

func (bc *BitmapContainer) Contains(value uint16) bool {
	wordIndex := value / 64
	bitIndex := value % 64
	return (bc.bitmap[wordIndex] & (1 << bitIndex)) != 0
}

func (bc *BitmapContainer) And(other Container) Container {
	if otherBc, ok := other.(*BitmapContainer); ok {
		result := &BitmapContainer{}
		for i := 0; i < 1024; i++ {
			result.bitmap[i] = bc.bitmap[i] & otherBc.bitmap[i]
		}

		// Convert to array if sparse
		if result.Count() < 4096 {
			ac := &ArrayContainer{values: make([]uint16, 0)}
			for _, v := range result.ToSlice() {
				ac.values = append(ac.values, v)
			}
			return ac
		}

		return result
	}

	// Handle BitmapContainer AND ArrayContainer
	result := &ArrayContainer{values: make([]uint16, 0)}
	if otherAc, ok := other.(*ArrayContainer); ok {
		for _, v := range otherAc.values {
			if bc.Contains(v) {
				result.values = append(result.values, v)
			}
		}
	}
	return result
}

func (bc *BitmapContainer) Or(other Container) Container {
	if otherBc, ok := other.(*BitmapContainer); ok {
		result := &BitmapContainer{}
		for i := 0; i < 1024; i++ {
			result.bitmap[i] = bc.bitmap[i] | otherBc.bitmap[i]
		}
		return result
	}

	// Handle BitmapContainer OR ArrayContainer
	result := &BitmapContainer{}
	result.bitmap = bc.bitmap // Copy current bitmap

	if otherAc, ok := other.(*ArrayContainer); ok {
		for _, v := range otherAc.values {
			result.Add(v)
		}
	}

	return result
}

func (bc *BitmapContainer) ToSlice() []uint16 {
	var result []uint16

	for wordIndex, word := range bc.bitmap {
		if word == 0 {
			continue
		}

		for bitIndex := 0; bitIndex < 64; bitIndex++ {
			if (word & (1 << bitIndex)) != 0 {
				value := uint16(wordIndex*64 + bitIndex)
				result = append(result, value)
			}
		}
	}

	return result
}

func (bc *BitmapContainer) Count() int {
	count := 0
	for _, word := range bc.bitmap {
		count += popcountUint64(word)
	}
	return count
}

func (bc *BitmapContainer) IsFull() bool {
	for _, word := range bc.bitmap {
		if word != 0xFFFFFFFFFFFFFFFF {
			return false
		}
	}
	return true
}

// Optimized popcount using built-in function
func popcountUint64(x uint64) int {
	// Use Brian Kernighan's algorithm for now
	// In production, use bits.OnesCount64(x) for better performance
	count := 0
	for x != 0 {
		x &= x - 1
		count++
	}
	return count
}

// Config holds configuration options for the UltraFast index
type Config struct {
	HashTableLoadFactor float64 // Default: 0.5
	KeySlotSize         int     // Default: 32 bytes
	CacheLineSize       int     // Default: 64 bytes
	Version             uint32  // Default: 1
}

// DefaultConfig returns the default configuration
func DefaultConfig() Config {
	return Config{
		HashTableLoadFactor: 0.5,
		KeySlotSize:         32,
		CacheLineSize:       64,
		Version:             1,
	}
}

// Stats holds performance statistics
type Stats struct {
	CacheHitRate   float64
	AvgLookupTime  time.Duration
	HashCollisions uint64
	TotalLookups   uint64
	MemoryMapped   bool
	IndexSize      int64
	UniqueKeys     uint32
}

// Generator creates UltraFast index files
type Generator struct {
	outputDir string
	config    Config
}

// NewGenerator creates a new index generator with default config
func NewGenerator(outputDir string) *Generator {
	return &Generator{
		outputDir: outputDir,
		config:    DefaultConfig(),
	}
}

// NewGeneratorWithConfig creates a new index generator with custom config
func NewGeneratorWithConfig(outputDir string, config Config) *Generator {
	return &Generator{
		outputDir: outputDir,
		config:    config,
	}
}

// Generate creates an UltraFast index file for the given column and data
func (g *Generator) Generate(columnName string, data []Record) error {
	// Build value map
	valueMap := make(map[string][]uint32)
	for _, record := range data {
		valueMap[record.Value] = append(valueMap[record.Value], record.LineNumber)
	}

	// Sort keys for optimal cache performance
	keys := make([]string, 0, len(valueMap))
	for key := range valueMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Create output file
	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", g.outputDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Build perfect hash table
	hashTableSize := nextPowerOf2(uint32(len(keys)) * 2) // Load factor 0.5
	hashTable := make([]uint32, hashTableSize)

	for i, key := range keys {
		hash := fastHash32(key) % hashTableSize
		// Linear probing for collision resolution
		for hashTable[hash] != 0 {
			hash = (hash + 1) % hashTableSize
		}
		hashTable[hash] = uint32(i + 1) // 1-based index (0 means empty)
	}

	// Write header
	header := g.buildHeader(uint32(len(keys)), hashTableSize)
	if _, err := file.Write(header); err != nil {
		return err
	}

	// Write hash table
	hashTableBytes := make([]byte, hashTableSize*4)
	for i, val := range hashTable {
		binary.BigEndian.PutUint32(hashTableBytes[i*4:(i+1)*4], val)
	}
	if _, err := file.Write(hashTableBytes); err != nil {
		return err
	}

	// Calculate data section offset
	dataOffset := 64 + int(hashTableSize*4) + len(keys)*g.config.KeySlotSize

	// Write key directory
	for _, key := range keys {
		keyBytes := make([]byte, g.config.KeySlotSize)
		copy(keyBytes, key)

		// Write offset and count in the last 8 bytes
		binary.BigEndian.PutUint32(keyBytes[g.config.KeySlotSize-8:g.config.KeySlotSize-4], uint32(dataOffset))
		binary.BigEndian.PutUint32(keyBytes[g.config.KeySlotSize-4:], uint32(len(valueMap[key])))

		if _, err := file.Write(keyBytes); err != nil {
			return err
		}

		dataOffset += 4 + len(valueMap[key])*4
	}

	// Write data section (line number arrays)
	for _, key := range keys {
		lineNums := valueMap[key]

		// Write count
		countBytes := make([]byte, 4)
		binary.BigEndian.PutUint32(countBytes, uint32(len(lineNums)))
		if _, err := file.Write(countBytes); err != nil {
			return err
		}

		// Write line numbers
		for _, lineNum := range lineNums {
			lineBytes := make([]byte, 4)
			binary.BigEndian.PutUint32(lineBytes, lineNum)
			if _, err := file.Write(lineBytes); err != nil {
				return err
			}
		}
	}

	return nil
}

// buildHeader creates the 64-byte header
func (g *Generator) buildHeader(numKeys, hashTableSize uint32) []byte {
	header := make([]byte, 64)

	// Magic number
	copy(header[0:8], []byte("ULTRAFAS"))

	// Metadata
	binary.BigEndian.PutUint32(header[8:12], numKeys)
	binary.BigEndian.PutUint32(header[12:16], 64)   // Header size
	binary.BigEndian.PutUint32(header[16:20], 127)  // Max keys per node (unused)
	binary.BigEndian.PutUint32(header[20:24], 4096) // Node size
	binary.BigEndian.PutUint32(header[24:28], hashTableSize)
	binary.BigEndian.PutUint32(header[28:32], uint32(g.config.KeySlotSize))
	binary.BigEndian.PutUint32(header[32:36], g.config.Version)
	binary.BigEndian.PutUint32(header[36:40], 0) // Flags

	// Calculate and store checksum (excluding checksum field itself)
	checksum := crc32.ChecksumIEEE(header[0:40])
	binary.BigEndian.PutUint32(header[40:44], checksum)

	return header
}

// QueryEngine performs searches on UltraFast index files
type QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
	stats    Stats
	config   Config
	rowStore *RowStore
}

// NewQueryEngine creates a new query engine
func NewQueryEngine(indexDir string) *QueryEngine {
	return &QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultConfig(),
		rowStore: NewRowStore(indexDir),
	}
}

// NewQueryEngineWithRowStore creates a new query engine with custom row store
func NewQueryEngineWithRowStore(indexDir string, rowStore *RowStore) *QueryEngine {
	return &QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultConfig(),
		rowStore: rowStore,
	}
}

// Search performs a search for the given value in the specified column
func (e *QueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	start := time.Now()
	defer func() {
		e.stats.TotalLookups++
		e.stats.AvgLookupTime = time.Since(start)
	}()

	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", e.indexDir, columnName)

	// Get memory-mapped data
	data, err := e.getMmapData(filename)
	if err != nil {
		return nil, err
	}

	// Validate header
	if err := e.validateHeader(data); err != nil {
		return nil, err
	}

	// Parse header
	hashTableSize := binary.BigEndian.Uint32(data[24:28])
	keySlotSize := int(binary.BigEndian.Uint32(data[28:32]))

	// Perfect hash lookup using optimized hash function
	hash := fastHash32(searchValue) % hashTableSize
	hashTableOffset := 64

	// Linear probing to find the key
	for {
		entryOffset := hashTableOffset + int(hash)*4
		if entryOffset+4 > len(data) {
			return []uint32{}, nil // Not found
		}

		keyIndex := binary.BigEndian.Uint32(data[entryOffset : entryOffset+4])
		if keyIndex == 0 {
			return []uint32{}, nil // Empty slot, key not found
		}

		// Check if this is our key
		keyOffset := hashTableOffset + int(hashTableSize)*4 + int(keyIndex-1)*keySlotSize
		if keyOffset+keySlotSize > len(data) {
			return []uint32{}, nil
		}

		// Fast string comparison
		keyData := data[keyOffset : keyOffset+keySlotSize]
		keyLen := 0
		for i, b := range keyData[:keySlotSize-8] { // Exclude offset/count fields
			if b == 0 {
				keyLen = i
				break
			}
		}

		if keyLen == len(searchValue) && fastStringCompare(searchValue, keyData[:keyLen]) {
			// Found the key! Get line numbers
			dataOffset := binary.BigEndian.Uint32(keyData[keySlotSize-8 : keySlotSize-4])
			count := binary.BigEndian.Uint32(keyData[keySlotSize-4:])

			// Read line numbers
			lineNumsOffset := int(dataOffset) + 4 // Skip count field
			if lineNumsOffset+int(count)*4 > len(data) {
				return nil, fmt.Errorf("data corruption: invalid offset")
			}

			results := make([]uint32, count)
			for i := uint32(0); i < count; i++ {
				offset := lineNumsOffset + int(i)*4
				results[i] = binary.BigEndian.Uint32(data[offset : offset+4])
			}

			e.stats.CacheHitRate = 1.0 // Found in cache
			return results, nil
		}

		// Linear probing - try next slot
		hash = (hash + 1) % hashTableSize
		e.stats.HashCollisions++
	}
}

// getMmapData returns memory-mapped data for the file, caching it for reuse
func (e *QueryEngine) getMmapData(filename string) ([]byte, error) {
	if data, exists := e.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	// Memory map the file
	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	e.mmapData[filename] = data
	e.stats.MemoryMapped = true
	e.stats.IndexSize = stat.Size()

	return data, nil
}

// validateHeader validates the file header
func (e *QueryEngine) validateHeader(data []byte) error {
	if len(data) < 64 {
		return fmt.Errorf("file too small for header")
	}

	if string(data[0:8]) != "ULTRAFAS" {
		return fmt.Errorf("invalid magic number")
	}

	headerSize := binary.BigEndian.Uint32(data[12:16])
	if headerSize != 64 {
		return fmt.Errorf("invalid header size: %d", headerSize)
	}

	// Validate checksum
	expectedChecksum := binary.BigEndian.Uint32(data[40:44])
	actualChecksum := crc32.ChecksumIEEE(data[0:40])
	if expectedChecksum != actualChecksum {
		return fmt.Errorf("header checksum mismatch")
	}

	return nil
}

// GetStats returns performance statistics
func (e *QueryEngine) GetStats() Stats {
	return e.stats
}

// ExecuteQuery executes a multi-column query and returns complete rows
func (e *QueryEngine) ExecuteQuery(tableName string, query *QueryRequest) (*QueryResult, error) {
	start := time.Now()

	result := &QueryResult{
		Rows: []map[string]string{},
		Stats: QueryStats{
			IndexesUsed:   []string{},
			RowsScanned:   0,
			IndexLookups:  0,
			SetOperations: 0,
			CacheHitRate:  0.0,
			ExecutionPlan: "",
		},
	}

	// Execute filter expression to get matching line numbers
	matchingLines, err := e.executeFilterExpression(query.Filter, &result.Stats)
	if err != nil {
		return nil, err
	}

	result.TotalMatches = len(matchingLines)

	// Apply offset and limit
	if query.Offset > 0 {
		if query.Offset >= len(matchingLines) {
			matchingLines = []uint32{}
		} else {
			matchingLines = matchingLines[query.Offset:]
		}
	}

	if query.Limit > 0 && len(matchingLines) > query.Limit {
		matchingLines = matchingLines[:query.Limit]
	}

	// Retrieve complete rows
	if len(matchingLines) > 0 {
		if len(query.SelectCols) > 0 {
			// Project only requested columns
			rows, err := e.rowStore.GetRowsWithProjection(tableName, matchingLines, query.SelectCols)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		} else {
			// Return all columns
			rows, err := e.rowStore.GetRows(tableName, matchingLines)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		}
	}

	result.QueryTime = time.Since(start)
	result.Stats.RowsScanned = len(result.Rows)

	return result, nil
}

// executeFilterExpression recursively executes a filter expression
func (e *QueryEngine) executeFilterExpression(filter *FilterExpression, stats *QueryStats) ([]uint32, error) {
	if filter == nil {
		return []uint32{}, nil
	}

	switch filter.Type {
	case FilterTypeLeaf:
		// Single column=value lookup
		stats.IndexLookups++
		stats.IndexesUsed = append(stats.IndexesUsed, filter.Column)

		results, err := e.Search(filter.Column, filter.Value)
		if err != nil {
			return nil, err
		}

		return results, nil

	case FilterTypeAnd:
		// AND operation - intersection of results
		stats.SetOperations++

		leftResults, err := e.executeFilterExpression(filter.Left, stats)
		if err != nil {
			return nil, err
		}

		rightResults, err := e.executeFilterExpression(filter.Right, stats)
		if err != nil {
			return nil, err
		}

		return e.intersectResults(leftResults, rightResults), nil

	case FilterTypeOr:
		// OR operation - union of results
		stats.SetOperations++

		leftResults, err := e.executeFilterExpression(filter.Left, stats)
		if err != nil {
			return nil, err
		}

		rightResults, err := e.executeFilterExpression(filter.Right, stats)
		if err != nil {
			return nil, err
		}

		return e.unionResults(leftResults, rightResults), nil

	default:
		return nil, fmt.Errorf("unknown filter type: %d", filter.Type)
	}
}

// intersectResults performs intersection of two result sets using bitmaps for large sets
func (e *QueryEngine) intersectResults(left, right []uint32) []uint32 {
	if len(left) == 0 || len(right) == 0 {
		return []uint32{}
	}

	// Use bitmap optimization for large result sets
	if len(left) > 1000 || len(right) > 1000 {
		return e.intersectResultsBitmap(left, right)
	}

	// Use maps for smaller sets
	leftSet := make(map[uint32]bool)
	for _, lineNum := range left {
		leftSet[lineNum] = true
	}

	var result []uint32
	for _, lineNum := range right {
		if leftSet[lineNum] {
			result = append(result, lineNum)
		}
	}

	return result
}

// unionResults performs union of two result sets using bitmaps for large sets
func (e *QueryEngine) unionResults(left, right []uint32) []uint32 {
	if len(left) == 0 {
		return right
	}
	if len(right) == 0 {
		return left
	}

	// Use bitmap optimization for large result sets
	if len(left) > 1000 || len(right) > 1000 {
		return e.unionResultsBitmap(left, right)
	}

	// Use map to avoid duplicates for smaller sets
	resultSet := make(map[uint32]bool)
	for _, lineNum := range left {
		resultSet[lineNum] = true
	}
	for _, lineNum := range right {
		resultSet[lineNum] = true
	}

	var result []uint32
	for lineNum := range resultSet {
		result = append(result, lineNum)
	}

	// Sort for consistent results
	sort.Slice(result, func(i, j int) bool {
		return result[i] < result[j]
	})

	return result
}

// intersectResultsBitmap performs intersection using bitmap operations
func (e *QueryEngine) intersectResultsBitmap(left, right []uint32) []uint32 {
	if len(left) == 0 || len(right) == 0 {
		return []uint32{}
	}

	// Create roaring bitmaps
	leftBitmap := NewRoaringBitmap()
	rightBitmap := NewRoaringBitmap()

	// Set bits for left set
	for _, line := range left {
		leftBitmap.Add(line)
	}

	// Set bits for right set
	for _, line := range right {
		rightBitmap.Add(line)
	}

	// Perform intersection
	resultBitmap := leftBitmap.And(rightBitmap)

	return resultBitmap.ToSlice()
}

// unionResultsBitmap performs union using bitmap operations
func (e *QueryEngine) unionResultsBitmap(left, right []uint32) []uint32 {
	if len(left) == 0 {
		return right
	}
	if len(right) == 0 {
		return left
	}

	// Create roaring bitmaps
	leftBitmap := NewRoaringBitmap()
	rightBitmap := NewRoaringBitmap()

	// Set bits for left set
	for _, line := range left {
		leftBitmap.Add(line)
	}

	// Set bits for right set
	for _, line := range right {
		rightBitmap.Add(line)
	}

	// Perform union
	resultBitmap := leftBitmap.Or(rightBitmap)

	return resultBitmap.ToSlice()
}

// ExecuteQueryWithBitmaps executes a query using bitmap optimization throughout
func (e *QueryEngine) ExecuteQueryWithBitmaps(tableName string, query *QueryRequest, maxLineNumber uint32) (*QueryResult, error) {
	start := time.Now()

	result := &QueryResult{
		Rows: []map[string]string{},
		Stats: QueryStats{
			IndexesUsed:   []string{},
			RowsScanned:   0,
			IndexLookups:  0,
			SetOperations: 0,
			CacheHitRate:  0.0,
			ExecutionPlan: "Bitmap-optimized execution",
		},
	}

	// Execute filter expression using bitmaps
	resultBitmap, err := e.executeFilterExpressionBitmap(query.Filter, maxLineNumber, &result.Stats)
	if err != nil {
		return nil, err
	}

	matchingLines := resultBitmap.ToSlice()
	result.TotalMatches = len(matchingLines)

	// Apply offset and limit
	if query.Offset > 0 {
		if query.Offset >= len(matchingLines) {
			matchingLines = []uint32{}
		} else {
			matchingLines = matchingLines[query.Offset:]
		}
	}

	if query.Limit > 0 && len(matchingLines) > query.Limit {
		matchingLines = matchingLines[:query.Limit]
	}

	// Retrieve complete rows
	if len(matchingLines) > 0 {
		if len(query.SelectCols) > 0 {
			rows, err := e.rowStore.GetRowsWithProjection(tableName, matchingLines, query.SelectCols)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		} else {
			rows, err := e.rowStore.GetRows(tableName, matchingLines)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		}
	}

	result.QueryTime = time.Since(start)
	result.Stats.RowsScanned = len(result.Rows)

	return result, nil
}

// executeFilterExpressionBitmap executes filter expressions using bitmaps
func (e *QueryEngine) executeFilterExpressionBitmap(filter *FilterExpression, maxLineNumber uint32, stats *QueryStats) (*RoaringBitmap, error) {
	if filter == nil {
		return NewRoaringBitmap(), nil
	}

	switch filter.Type {
	case FilterTypeLeaf:
		// Single column=value lookup
		stats.IndexLookups++
		stats.IndexesUsed = append(stats.IndexesUsed, filter.Column)

		results, err := e.Search(filter.Column, filter.Value)
		if err != nil {
			return nil, err
		}

		// Convert to bitmap
		bitmap := NewRoaringBitmap()
		for _, line := range results {
			bitmap.Add(line)
		}

		return bitmap, nil

	case FilterTypeAnd:
		// AND operation - intersection of bitmaps
		stats.SetOperations++

		leftBitmap, err := e.executeFilterExpressionBitmap(filter.Left, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		rightBitmap, err := e.executeFilterExpressionBitmap(filter.Right, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		return leftBitmap.And(rightBitmap), nil

	case FilterTypeOr:
		// OR operation - union of bitmaps
		stats.SetOperations++

		leftBitmap, err := e.executeFilterExpressionBitmap(filter.Left, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		rightBitmap, err := e.executeFilterExpressionBitmap(filter.Right, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		return leftBitmap.Or(rightBitmap), nil

	default:
		return nil, fmt.Errorf("unknown filter type: %d", filter.Type)
	}
}

// Close releases memory-mapped files
func (e *QueryEngine) Close() error {
	for filename, data := range e.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	e.mmapData = make(map[string][]byte)

	if e.rowStore != nil {
		return e.rowStore.Close()
	}

	return nil
}

// Utility functions

// nextPowerOf2 returns the next power of 2 greater than or equal to n
func nextPowerOf2(n uint32) uint32 {
	n--
	n |= n >> 1
	n |= n >> 2
	n |= n >> 4
	n |= n >> 8
	n |= n >> 16
	return n + 1
}

// fnvHash32 implements the FNV-1a hash function with SIMD optimization
func fnvHash32(s string) uint32 {
	const (
		fnvPrime  = 16777619
		fnvOffset = 2166136261
	)

	if len(s) == 0 {
		return fnvOffset
	}

	// Use unsafe to avoid string to []byte conversion
	sBytes := *(*[]byte)(unsafe.Pointer(&s))

	hash := uint32(fnvOffset)

	// Process 8 bytes at a time for better performance
	i := 0
	for i+8 <= len(sBytes) {
		// Load 8 bytes as uint64
		chunk := *(*uint64)(unsafe.Pointer(&sBytes[i]))

		// Process each byte in the chunk
		for j := 0; j < 8; j++ {
			hash ^= uint32(chunk & 0xFF)
			hash *= fnvPrime
			chunk >>= 8
		}
		i += 8
	}

	// Process remaining bytes
	for i < len(sBytes) {
		hash ^= uint32(sBytes[i])
		hash *= fnvPrime
		i++
	}

	return hash
}

// fastHash32 implements a fast hash optimized for performance
func fastHash32(s string) uint32 {
	if len(s) == 0 {
		return 0
	}

	// Use unsafe to avoid string to []byte conversion
	sBytes := *(*[]byte)(unsafe.Pointer(&s))

	var hash uint32 = 2166136261 // FNV offset basis

	// Process 8 bytes at a time for better performance
	i := 0
	for i+8 <= len(sBytes) {
		// Load 8 bytes as uint64 and process
		chunk := *(*uint64)(unsafe.Pointer(&sBytes[i]))

		// Mix the bytes using bit operations
		hash ^= uint32(chunk)
		hash *= 16777619 // FNV prime
		hash ^= uint32(chunk >> 32)
		hash *= 16777619

		i += 8
	}

	// Process remaining 4 bytes
	if i+4 <= len(sBytes) {
		chunk := *(*uint32)(unsafe.Pointer(&sBytes[i]))
		hash ^= chunk
		hash *= 16777619
		i += 4
	}

	// Process remaining bytes
	for i < len(sBytes) {
		hash ^= uint32(sBytes[i])
		hash *= 16777619
		i++
	}

	// Final mixing
	hash ^= hash >> 16
	hash *= 0x85ebca6b
	hash ^= hash >> 13
	hash *= 0xc2b2ae35
	hash ^= hash >> 16

	return hash
}

// rotateLeft32 rotates a 32-bit integer left by n bits
func rotateLeft32(x uint32, n int) uint32 {
	return (x << n) | (x >> (32 - n))
}

// fastStringCompare performs SIMD-optimized string comparison
func fastStringCompare(s string, b []byte) bool {
	if len(s) != len(b) {
		return false
	}

	// Convert string to byte slice using unsafe
	sBytes := *(*[]byte)(unsafe.Pointer(&s))

	// Compare 8 bytes at a time when possible
	i := 0
	for i+8 <= len(sBytes) {
		if *(*uint64)(unsafe.Pointer(&sBytes[i])) !=
			*(*uint64)(unsafe.Pointer(&b[i])) {
			return false
		}
		i += 8
	}

	// Compare remaining bytes
	for i < len(sBytes) {
		if sBytes[i] != b[i] {
			return false
		}
		i++
	}

	return true
}
