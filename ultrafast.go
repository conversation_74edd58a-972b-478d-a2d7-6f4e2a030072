// Package ultrafast provides high-performance indexing with memory-mapped perfect hash tables
package main

import (
	"encoding/binary"
	"fmt"
	"hash/crc32"
	"os"
	"sort"
	"syscall"
	"time"
	"unsafe"
)

// Record represents a single data record for indexing
type Record struct {
	LineNumber uint32
	Value      string
}

// RowRecord represents a complete row with all column values
type RowRecord struct {
	LineNumber uint32
	Values     map[string]string // column_name -> value
}

// FilterExpression represents a query filter with AND/OR logic
type FilterExpression struct {
	Type     FilterType
	Column   string
	Value    string
	Left     *FilterExpression
	Right    *FilterExpression
	Operator LogicalOperator
}

// FilterType represents the type of filter
type FilterType int

const (
	FilterTypeLeaf FilterType = iota // Single column=value condition
	FilterTypeAnd                    // AND operation
	FilterTypeOr                     // OR operation
)

// LogicalOperator represents logical operators
type LogicalOperator int

const (
	LogicalOperatorAnd LogicalOperator = iota
	LogicalOperatorOr
)

// QueryRequest represents a multi-column query request
type QueryRequest struct {
	Filter     *FilterExpression
	SelectCols []string // Columns to return in results
	Limit      int      // Maximum number of results (0 = no limit)
	Offset     int      // Number of results to skip
}

// QueryResult represents the result of a multi-column query
type QueryResult struct {
	Rows         []map[string]string // Each row as column_name -> value
	TotalMatches int                 // Total matches before limit/offset
	QueryTime    time.Duration       // Time taken to execute query
	Stats        QueryStats          // Detailed execution statistics
}

// QueryStats provides detailed query execution statistics
type QueryStats struct {
	IndexesUsed   []string // Which column indexes were accessed
	RowsScanned   int      // Number of rows examined
	IndexLookups  int      // Number of index lookups performed
	SetOperations int      // Number of AND/OR operations
	CacheHitRate  float64  // Cache hit rate across all indexes
	ExecutionPlan string   // Human-readable execution plan
}

// BitSet represents an efficient set of line numbers using bitmaps
type BitSet struct {
	bits   []uint64
	maxBit uint32
}

// NewBitSet creates a new BitSet with the specified maximum bit
func NewBitSet(maxBit uint32) *BitSet {
	size := (maxBit + 63) / 64 // Round up to nearest 64-bit word
	return &BitSet{
		bits:   make([]uint64, size),
		maxBit: maxBit,
	}
}

// Set sets the bit at the given position
func (bs *BitSet) Set(bit uint32) {
	if bit > bs.maxBit {
		return
	}
	wordIndex := bit / 64
	bitIndex := bit % 64
	bs.bits[wordIndex] |= 1 << bitIndex
}

// IsSet returns true if the bit at the given position is set
func (bs *BitSet) IsSet(bit uint32) bool {
	if bit > bs.maxBit {
		return false
	}
	wordIndex := bit / 64
	bitIndex := bit % 64
	return (bs.bits[wordIndex] & (1 << bitIndex)) != 0
}

// And performs bitwise AND with another BitSet
func (bs *BitSet) And(other *BitSet) *BitSet {
	maxBit := bs.maxBit
	if other.maxBit > maxBit {
		maxBit = other.maxBit
	}

	result := NewBitSet(maxBit)
	minWords := len(bs.bits)
	if len(other.bits) < minWords {
		minWords = len(other.bits)
	}

	for i := 0; i < minWords; i++ {
		result.bits[i] = bs.bits[i] & other.bits[i]
	}

	return result
}

// Or performs bitwise OR with another BitSet
func (bs *BitSet) Or(other *BitSet) *BitSet {
	maxBit := bs.maxBit
	if other.maxBit > maxBit {
		maxBit = other.maxBit
	}

	result := NewBitSet(maxBit)

	// Copy all bits from both sets
	for i := 0; i < len(bs.bits) && i < len(result.bits); i++ {
		result.bits[i] = bs.bits[i]
	}

	for i := 0; i < len(other.bits) && i < len(result.bits); i++ {
		result.bits[i] |= other.bits[i]
	}

	return result
}

// ToSlice returns all set bits as a slice of uint32
func (bs *BitSet) ToSlice() []uint32 {
	var result []uint32

	for wordIndex, word := range bs.bits {
		if word == 0 {
			continue
		}

		for bitIndex := 0; bitIndex < 64; bitIndex++ {
			if (word & (1 << bitIndex)) != 0 {
				bit := uint32(wordIndex*64 + bitIndex)
				if bit <= bs.maxBit {
					result = append(result, bit)
				}
			}
		}
	}

	return result
}

// Count returns the number of set bits
func (bs *BitSet) Count() int {
	count := 0
	for _, word := range bs.bits {
		count += popcount(word)
	}
	return count
}

// popcount counts the number of set bits in a uint64
func popcount(x uint64) int {
	// Brian Kernighan's algorithm
	count := 0
	for x != 0 {
		x &= x - 1
		count++
	}
	return count
}

// Config holds configuration options for the UltraFast index
type Config struct {
	HashTableLoadFactor float64 // Default: 0.5
	KeySlotSize         int     // Default: 32 bytes
	CacheLineSize       int     // Default: 64 bytes
	Version             uint32  // Default: 1
}

// DefaultConfig returns the default configuration
func DefaultConfig() Config {
	return Config{
		HashTableLoadFactor: 0.5,
		KeySlotSize:         32,
		CacheLineSize:       64,
		Version:             1,
	}
}

// Stats holds performance statistics
type Stats struct {
	CacheHitRate   float64
	AvgLookupTime  time.Duration
	HashCollisions uint64
	TotalLookups   uint64
	MemoryMapped   bool
	IndexSize      int64
	UniqueKeys     uint32
}

// Generator creates UltraFast index files
type Generator struct {
	outputDir string
	config    Config
}

// NewGenerator creates a new index generator with default config
func NewGenerator(outputDir string) *Generator {
	return &Generator{
		outputDir: outputDir,
		config:    DefaultConfig(),
	}
}

// NewGeneratorWithConfig creates a new index generator with custom config
func NewGeneratorWithConfig(outputDir string, config Config) *Generator {
	return &Generator{
		outputDir: outputDir,
		config:    config,
	}
}

// Generate creates an UltraFast index file for the given column and data
func (g *Generator) Generate(columnName string, data []Record) error {
	// Build value map
	valueMap := make(map[string][]uint32)
	for _, record := range data {
		valueMap[record.Value] = append(valueMap[record.Value], record.LineNumber)
	}

	// Sort keys for optimal cache performance
	keys := make([]string, 0, len(valueMap))
	for key := range valueMap {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	// Create output file
	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", g.outputDir, columnName)
	file, err := os.Create(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	// Build perfect hash table
	hashTableSize := nextPowerOf2(uint32(len(keys)) * 2) // Load factor 0.5
	hashTable := make([]uint32, hashTableSize)

	for i, key := range keys {
		hash := fnvHash32(key) % hashTableSize
		// Linear probing for collision resolution
		for hashTable[hash] != 0 {
			hash = (hash + 1) % hashTableSize
		}
		hashTable[hash] = uint32(i + 1) // 1-based index (0 means empty)
	}

	// Write header
	header := g.buildHeader(uint32(len(keys)), hashTableSize)
	if _, err := file.Write(header); err != nil {
		return err
	}

	// Write hash table
	hashTableBytes := make([]byte, hashTableSize*4)
	for i, val := range hashTable {
		binary.BigEndian.PutUint32(hashTableBytes[i*4:(i+1)*4], val)
	}
	if _, err := file.Write(hashTableBytes); err != nil {
		return err
	}

	// Calculate data section offset
	dataOffset := 64 + int(hashTableSize*4) + len(keys)*g.config.KeySlotSize

	// Write key directory
	for _, key := range keys {
		keyBytes := make([]byte, g.config.KeySlotSize)
		copy(keyBytes, key)

		// Write offset and count in the last 8 bytes
		binary.BigEndian.PutUint32(keyBytes[g.config.KeySlotSize-8:g.config.KeySlotSize-4], uint32(dataOffset))
		binary.BigEndian.PutUint32(keyBytes[g.config.KeySlotSize-4:], uint32(len(valueMap[key])))

		if _, err := file.Write(keyBytes); err != nil {
			return err
		}

		dataOffset += 4 + len(valueMap[key])*4
	}

	// Write data section (line number arrays)
	for _, key := range keys {
		lineNums := valueMap[key]

		// Write count
		countBytes := make([]byte, 4)
		binary.BigEndian.PutUint32(countBytes, uint32(len(lineNums)))
		if _, err := file.Write(countBytes); err != nil {
			return err
		}

		// Write line numbers
		for _, lineNum := range lineNums {
			lineBytes := make([]byte, 4)
			binary.BigEndian.PutUint32(lineBytes, lineNum)
			if _, err := file.Write(lineBytes); err != nil {
				return err
			}
		}
	}

	return nil
}

// buildHeader creates the 64-byte header
func (g *Generator) buildHeader(numKeys, hashTableSize uint32) []byte {
	header := make([]byte, 64)

	// Magic number
	copy(header[0:8], []byte("ULTRAFAS"))

	// Metadata
	binary.BigEndian.PutUint32(header[8:12], numKeys)
	binary.BigEndian.PutUint32(header[12:16], 64)   // Header size
	binary.BigEndian.PutUint32(header[16:20], 127)  // Max keys per node (unused)
	binary.BigEndian.PutUint32(header[20:24], 4096) // Node size
	binary.BigEndian.PutUint32(header[24:28], hashTableSize)
	binary.BigEndian.PutUint32(header[28:32], uint32(g.config.KeySlotSize))
	binary.BigEndian.PutUint32(header[32:36], g.config.Version)
	binary.BigEndian.PutUint32(header[36:40], 0) // Flags

	// Calculate and store checksum (excluding checksum field itself)
	checksum := crc32.ChecksumIEEE(header[0:40])
	binary.BigEndian.PutUint32(header[40:44], checksum)

	return header
}

// QueryEngine performs searches on UltraFast index files
type QueryEngine struct {
	indexDir string
	mmapData map[string][]byte
	stats    Stats
	config   Config
	rowStore *RowStore
}

// NewQueryEngine creates a new query engine
func NewQueryEngine(indexDir string) *QueryEngine {
	return &QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultConfig(),
		rowStore: NewRowStore(indexDir),
	}
}

// NewQueryEngineWithRowStore creates a new query engine with custom row store
func NewQueryEngineWithRowStore(indexDir string, rowStore *RowStore) *QueryEngine {
	return &QueryEngine{
		indexDir: indexDir,
		mmapData: make(map[string][]byte),
		config:   DefaultConfig(),
		rowStore: rowStore,
	}
}

// Search performs a search for the given value in the specified column
func (e *QueryEngine) Search(columnName, searchValue string) ([]uint32, error) {
	start := time.Now()
	defer func() {
		e.stats.TotalLookups++
		e.stats.AvgLookupTime = time.Since(start)
	}()

	filename := fmt.Sprintf("%s/%s_ultrafast.ufidx", e.indexDir, columnName)

	// Get memory-mapped data
	data, err := e.getMmapData(filename)
	if err != nil {
		return nil, err
	}

	// Validate header
	if err := e.validateHeader(data); err != nil {
		return nil, err
	}

	// Parse header
	hashTableSize := binary.BigEndian.Uint32(data[24:28])
	keySlotSize := int(binary.BigEndian.Uint32(data[28:32]))

	// Perfect hash lookup
	hash := fnvHash32(searchValue) % hashTableSize
	hashTableOffset := 64

	// Linear probing to find the key
	for {
		entryOffset := hashTableOffset + int(hash)*4
		if entryOffset+4 > len(data) {
			return []uint32{}, nil // Not found
		}

		keyIndex := binary.BigEndian.Uint32(data[entryOffset : entryOffset+4])
		if keyIndex == 0 {
			return []uint32{}, nil // Empty slot, key not found
		}

		// Check if this is our key
		keyOffset := hashTableOffset + int(hashTableSize)*4 + int(keyIndex-1)*keySlotSize
		if keyOffset+keySlotSize > len(data) {
			return []uint32{}, nil
		}

		// Fast string comparison
		keyData := data[keyOffset : keyOffset+keySlotSize]
		keyLen := 0
		for i, b := range keyData[:keySlotSize-8] { // Exclude offset/count fields
			if b == 0 {
				keyLen = i
				break
			}
		}

		if keyLen == len(searchValue) && fastStringCompare(searchValue, keyData[:keyLen]) {
			// Found the key! Get line numbers
			dataOffset := binary.BigEndian.Uint32(keyData[keySlotSize-8 : keySlotSize-4])
			count := binary.BigEndian.Uint32(keyData[keySlotSize-4:])

			// Read line numbers
			lineNumsOffset := int(dataOffset) + 4 // Skip count field
			if lineNumsOffset+int(count)*4 > len(data) {
				return nil, fmt.Errorf("data corruption: invalid offset")
			}

			results := make([]uint32, count)
			for i := uint32(0); i < count; i++ {
				offset := lineNumsOffset + int(i)*4
				results[i] = binary.BigEndian.Uint32(data[offset : offset+4])
			}

			e.stats.CacheHitRate = 1.0 // Found in cache
			return results, nil
		}

		// Linear probing - try next slot
		hash = (hash + 1) % hashTableSize
		e.stats.HashCollisions++
	}
}

// getMmapData returns memory-mapped data for the file, caching it for reuse
func (e *QueryEngine) getMmapData(filename string) ([]byte, error) {
	if data, exists := e.mmapData[filename]; exists {
		return data, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	stat, err := file.Stat()
	if err != nil {
		return nil, err
	}

	// Memory map the file
	data, err := syscall.Mmap(int(file.Fd()), 0, int(stat.Size()),
		syscall.PROT_READ, syscall.MAP_SHARED)
	if err != nil {
		return nil, err
	}

	e.mmapData[filename] = data
	e.stats.MemoryMapped = true
	e.stats.IndexSize = stat.Size()

	return data, nil
}

// validateHeader validates the file header
func (e *QueryEngine) validateHeader(data []byte) error {
	if len(data) < 64 {
		return fmt.Errorf("file too small for header")
	}

	if string(data[0:8]) != "ULTRAFAS" {
		return fmt.Errorf("invalid magic number")
	}

	headerSize := binary.BigEndian.Uint32(data[12:16])
	if headerSize != 64 {
		return fmt.Errorf("invalid header size: %d", headerSize)
	}

	// Validate checksum
	expectedChecksum := binary.BigEndian.Uint32(data[40:44])
	actualChecksum := crc32.ChecksumIEEE(data[0:40])
	if expectedChecksum != actualChecksum {
		return fmt.Errorf("header checksum mismatch")
	}

	return nil
}

// GetStats returns performance statistics
func (e *QueryEngine) GetStats() Stats {
	return e.stats
}

// ExecuteQuery executes a multi-column query and returns complete rows
func (e *QueryEngine) ExecuteQuery(tableName string, query *QueryRequest) (*QueryResult, error) {
	start := time.Now()

	result := &QueryResult{
		Rows: []map[string]string{},
		Stats: QueryStats{
			IndexesUsed:   []string{},
			RowsScanned:   0,
			IndexLookups:  0,
			SetOperations: 0,
			CacheHitRate:  0.0,
			ExecutionPlan: "",
		},
	}

	// Execute filter expression to get matching line numbers
	matchingLines, err := e.executeFilterExpression(query.Filter, &result.Stats)
	if err != nil {
		return nil, err
	}

	result.TotalMatches = len(matchingLines)

	// Apply offset and limit
	if query.Offset > 0 {
		if query.Offset >= len(matchingLines) {
			matchingLines = []uint32{}
		} else {
			matchingLines = matchingLines[query.Offset:]
		}
	}

	if query.Limit > 0 && len(matchingLines) > query.Limit {
		matchingLines = matchingLines[:query.Limit]
	}

	// Retrieve complete rows
	if len(matchingLines) > 0 {
		if len(query.SelectCols) > 0 {
			// Project only requested columns
			rows, err := e.rowStore.GetRowsWithProjection(tableName, matchingLines, query.SelectCols)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		} else {
			// Return all columns
			rows, err := e.rowStore.GetRows(tableName, matchingLines)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		}
	}

	result.QueryTime = time.Since(start)
	result.Stats.RowsScanned = len(result.Rows)

	return result, nil
}

// executeFilterExpression recursively executes a filter expression
func (e *QueryEngine) executeFilterExpression(filter *FilterExpression, stats *QueryStats) ([]uint32, error) {
	if filter == nil {
		return []uint32{}, nil
	}

	switch filter.Type {
	case FilterTypeLeaf:
		// Single column=value lookup
		stats.IndexLookups++
		stats.IndexesUsed = append(stats.IndexesUsed, filter.Column)

		results, err := e.Search(filter.Column, filter.Value)
		if err != nil {
			return nil, err
		}

		return results, nil

	case FilterTypeAnd:
		// AND operation - intersection of results
		stats.SetOperations++

		leftResults, err := e.executeFilterExpression(filter.Left, stats)
		if err != nil {
			return nil, err
		}

		rightResults, err := e.executeFilterExpression(filter.Right, stats)
		if err != nil {
			return nil, err
		}

		return e.intersectResults(leftResults, rightResults), nil

	case FilterTypeOr:
		// OR operation - union of results
		stats.SetOperations++

		leftResults, err := e.executeFilterExpression(filter.Left, stats)
		if err != nil {
			return nil, err
		}

		rightResults, err := e.executeFilterExpression(filter.Right, stats)
		if err != nil {
			return nil, err
		}

		return e.unionResults(leftResults, rightResults), nil

	default:
		return nil, fmt.Errorf("unknown filter type: %d", filter.Type)
	}
}

// intersectResults performs intersection of two result sets using bitmaps for large sets
func (e *QueryEngine) intersectResults(left, right []uint32) []uint32 {
	if len(left) == 0 || len(right) == 0 {
		return []uint32{}
	}

	// Use bitmap optimization for large result sets
	if len(left) > 1000 || len(right) > 1000 {
		return e.intersectResultsBitmap(left, right)
	}

	// Use maps for smaller sets
	leftSet := make(map[uint32]bool)
	for _, lineNum := range left {
		leftSet[lineNum] = true
	}

	var result []uint32
	for _, lineNum := range right {
		if leftSet[lineNum] {
			result = append(result, lineNum)
		}
	}

	return result
}

// unionResults performs union of two result sets using bitmaps for large sets
func (e *QueryEngine) unionResults(left, right []uint32) []uint32 {
	if len(left) == 0 {
		return right
	}
	if len(right) == 0 {
		return left
	}

	// Use bitmap optimization for large result sets
	if len(left) > 1000 || len(right) > 1000 {
		return e.unionResultsBitmap(left, right)
	}

	// Use map to avoid duplicates for smaller sets
	resultSet := make(map[uint32]bool)
	for _, lineNum := range left {
		resultSet[lineNum] = true
	}
	for _, lineNum := range right {
		resultSet[lineNum] = true
	}

	var result []uint32
	for lineNum := range resultSet {
		result = append(result, lineNum)
	}

	// Sort for consistent results
	sort.Slice(result, func(i, j int) bool {
		return result[i] < result[j]
	})

	return result
}

// intersectResultsBitmap performs intersection using bitmap operations
func (e *QueryEngine) intersectResultsBitmap(left, right []uint32) []uint32 {
	if len(left) == 0 || len(right) == 0 {
		return []uint32{}
	}

	// Find maximum line number to size bitmap
	maxLine := uint32(0)
	for _, line := range left {
		if line > maxLine {
			maxLine = line
		}
	}
	for _, line := range right {
		if line > maxLine {
			maxLine = line
		}
	}

	// Create bitmaps
	leftBitmap := NewBitSet(maxLine)
	rightBitmap := NewBitSet(maxLine)

	// Set bits for left set
	for _, line := range left {
		leftBitmap.Set(line)
	}

	// Set bits for right set
	for _, line := range right {
		rightBitmap.Set(line)
	}

	// Perform intersection
	resultBitmap := leftBitmap.And(rightBitmap)

	return resultBitmap.ToSlice()
}

// unionResultsBitmap performs union using bitmap operations
func (e *QueryEngine) unionResultsBitmap(left, right []uint32) []uint32 {
	if len(left) == 0 {
		return right
	}
	if len(right) == 0 {
		return left
	}

	// Find maximum line number to size bitmap
	maxLine := uint32(0)
	for _, line := range left {
		if line > maxLine {
			maxLine = line
		}
	}
	for _, line := range right {
		if line > maxLine {
			maxLine = line
		}
	}

	// Create bitmaps
	leftBitmap := NewBitSet(maxLine)
	rightBitmap := NewBitSet(maxLine)

	// Set bits for left set
	for _, line := range left {
		leftBitmap.Set(line)
	}

	// Set bits for right set
	for _, line := range right {
		rightBitmap.Set(line)
	}

	// Perform union
	resultBitmap := leftBitmap.Or(rightBitmap)

	return resultBitmap.ToSlice()
}

// ExecuteQueryWithBitmaps executes a query using bitmap optimization throughout
func (e *QueryEngine) ExecuteQueryWithBitmaps(tableName string, query *QueryRequest, maxLineNumber uint32) (*QueryResult, error) {
	start := time.Now()

	result := &QueryResult{
		Rows: []map[string]string{},
		Stats: QueryStats{
			IndexesUsed:   []string{},
			RowsScanned:   0,
			IndexLookups:  0,
			SetOperations: 0,
			CacheHitRate:  0.0,
			ExecutionPlan: "Bitmap-optimized execution",
		},
	}

	// Execute filter expression using bitmaps
	resultBitmap, err := e.executeFilterExpressionBitmap(query.Filter, maxLineNumber, &result.Stats)
	if err != nil {
		return nil, err
	}

	matchingLines := resultBitmap.ToSlice()
	result.TotalMatches = len(matchingLines)

	// Apply offset and limit
	if query.Offset > 0 {
		if query.Offset >= len(matchingLines) {
			matchingLines = []uint32{}
		} else {
			matchingLines = matchingLines[query.Offset:]
		}
	}

	if query.Limit > 0 && len(matchingLines) > query.Limit {
		matchingLines = matchingLines[:query.Limit]
	}

	// Retrieve complete rows
	if len(matchingLines) > 0 {
		if len(query.SelectCols) > 0 {
			rows, err := e.rowStore.GetRowsWithProjection(tableName, matchingLines, query.SelectCols)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		} else {
			rows, err := e.rowStore.GetRows(tableName, matchingLines)
			if err != nil {
				return nil, err
			}
			result.Rows = rows
		}
	}

	result.QueryTime = time.Since(start)
	result.Stats.RowsScanned = len(result.Rows)

	return result, nil
}

// executeFilterExpressionBitmap executes filter expressions using bitmaps
func (e *QueryEngine) executeFilterExpressionBitmap(filter *FilterExpression, maxLineNumber uint32, stats *QueryStats) (*BitSet, error) {
	if filter == nil {
		return NewBitSet(maxLineNumber), nil
	}

	switch filter.Type {
	case FilterTypeLeaf:
		// Single column=value lookup
		stats.IndexLookups++
		stats.IndexesUsed = append(stats.IndexesUsed, filter.Column)

		results, err := e.Search(filter.Column, filter.Value)
		if err != nil {
			return nil, err
		}

		// Convert to bitmap
		bitmap := NewBitSet(maxLineNumber)
		for _, line := range results {
			bitmap.Set(line)
		}

		return bitmap, nil

	case FilterTypeAnd:
		// AND operation - intersection of bitmaps
		stats.SetOperations++

		leftBitmap, err := e.executeFilterExpressionBitmap(filter.Left, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		rightBitmap, err := e.executeFilterExpressionBitmap(filter.Right, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		return leftBitmap.And(rightBitmap), nil

	case FilterTypeOr:
		// OR operation - union of bitmaps
		stats.SetOperations++

		leftBitmap, err := e.executeFilterExpressionBitmap(filter.Left, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		rightBitmap, err := e.executeFilterExpressionBitmap(filter.Right, maxLineNumber, stats)
		if err != nil {
			return nil, err
		}

		return leftBitmap.Or(rightBitmap), nil

	default:
		return nil, fmt.Errorf("unknown filter type: %d", filter.Type)
	}
}

// Close releases memory-mapped files
func (e *QueryEngine) Close() error {
	for filename, data := range e.mmapData {
		if err := syscall.Munmap(data); err != nil {
			return fmt.Errorf("failed to unmap %s: %v", filename, err)
		}
	}
	e.mmapData = make(map[string][]byte)

	if e.rowStore != nil {
		return e.rowStore.Close()
	}

	return nil
}

// Utility functions

// nextPowerOf2 returns the next power of 2 greater than or equal to n
func nextPowerOf2(n uint32) uint32 {
	n--
	n |= n >> 1
	n |= n >> 2
	n |= n >> 4
	n |= n >> 8
	n |= n >> 16
	return n + 1
}

// fnvHash32 implements the FNV-1a hash function
func fnvHash32(s string) uint32 {
	const (
		fnvPrime  = 16777619
		fnvOffset = 2166136261
	)
	hash := uint32(fnvOffset)
	for _, b := range []byte(s) {
		hash ^= uint32(b)
		hash *= fnvPrime
	}
	return hash
}

// fastStringCompare performs SIMD-optimized string comparison
func fastStringCompare(s string, b []byte) bool {
	if len(s) != len(b) {
		return false
	}

	// Convert string to byte slice using unsafe
	sBytes := *(*[]byte)(unsafe.Pointer(&s))

	// Compare 8 bytes at a time when possible
	i := 0
	for i+8 <= len(sBytes) {
		if *(*uint64)(unsafe.Pointer(&sBytes[i])) !=
			*(*uint64)(unsafe.Pointer(&b[i])) {
			return false
		}
		i += 8
	}

	// Compare remaining bytes
	for i < len(sBytes) {
		if sBytes[i] != b[i] {
			return false
		}
		i++
	}

	return true
}
