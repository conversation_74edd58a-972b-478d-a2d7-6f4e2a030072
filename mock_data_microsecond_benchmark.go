package main

import (
	"fmt"
	"os"
	"testing"
	"time"
)

func BenchmarkMockDataMicrosecondPerformance(b *testing.B) {
	// Setup - read the actual mock_data.csv
	tempDir := "/tmp/ultrafast_mock_microsecond_bench"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Read mock data
	rows, columns, err := readFullCSV("mock_data.csv")
	if err != nil {
		b.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	b.Logf("📊 Mock Data Statistics:")
	b.Logf("  Total rows: %d", len(rows))
	b.Logf("  Columns: %d", len(columns))

	// Extract key columns for indexing
	protocolData := extractColumnData(rows, "protocol")
	actionData := extractColumnData(rows, "action")
	ruleCategoryData := extractColumnData(rows, "rule_category")
	sourceCountryData := extractColumnData(rows, "source_country")

	b.Logf("  Protocol values: %d unique", len(getUniqueValues(protocolData)))
	b.Logf("  Action values: %d unique", len(getUniqueValues(actionData)))
	b.Logf("  Rule categories: %d unique", len(getUniqueValues(ruleCategoryData)))
	b.Logf("  Source countries: %d unique", len(getUniqueValues(sourceCountryData)))

	// Generate V1 indexes
	generatorV1 := NewGenerator(tempDir)
	start := time.Now()
	generatorV1.Generate("protocol", protocolData)
	generatorV1.Generate("action", actionData)
	generatorV1.Generate("rule_category", ruleCategoryData)
	generatorV1.Generate("source_country", sourceCountryData)
	v1IndexTime := time.Since(start)

	// Generate V2 indexes
	generatorV2 := NewV2Generator(tempDir)
	start = time.Now()
	generatorV2.GenerateV2("protocol", protocolData)
	generatorV2.GenerateV2("action", actionData)
	generatorV2.GenerateV2("rule_category", ruleCategoryData)
	generatorV2.GenerateV2("source_country", sourceCountryData)
	v2IndexTime := time.Since(start)

	b.Logf("⏱️  Index Generation Times:")
	b.Logf("  V1 Format: %v", v1IndexTime)
	b.Logf("  V2 Format: %v", v2IndexTime)

	// Create engines
	engineV1 := NewQueryEngine(tempDir)
	defer engineV1.Close()

	engineV2 := NewV2QueryEngine(tempDir)
	defer engineV2.Close()

	// Warm up engines
	for i := 0; i < 10; i++ {
		engineV1.Search("protocol", "TCP")
		engineV2.SearchV2("protocol", "TCP")
	}

	// Benchmark realistic queries
	b.Run("V1_TCP_Protocol", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("protocol", "TCP")
		}
	})

	b.Run("V2_TCP_Protocol", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("protocol", "TCP")
		}
	})

	b.Run("V1_Block_Action", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("action", "Block")
		}
	})

	b.Run("V2_Block_Action", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("action", "Block")
		}
	})

	b.Run("V1_WebFiltering_Category", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("rule_category", "Web Filtering")
		}
	})

	b.Run("V2_WebFiltering_Category", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("rule_category", "Web Filtering")
		}
	})

	b.Run("V1_China_Country", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("source_country", "China")
		}
	})

	b.Run("V2_China_Country", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("source_country", "China")
		}
	})

	// Negative searches (bloom filter advantage)
	b.Run("V1_Negative_Protocol", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("protocol", "NONEXISTENT_PROTOCOL")
		}
	})

	b.Run("V2_Negative_Protocol", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("protocol", "NONEXISTENT_PROTOCOL")
		}
	})

	b.Run("V1_Negative_Country", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("source_country", "NONEXISTENT_COUNTRY")
		}
	})

	b.Run("V2_Negative_Country", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("source_country", "NONEXISTENT_COUNTRY")
		}
	})
}

func TestMockDataDetailedPerformance(t *testing.T) {
	// Setup
	tempDir := "/tmp/ultrafast_mock_detailed_test"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Read mock data
	rows, columns, err := readFullCSV("mock_data.csv")
	if err != nil {
		t.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	t.Logf("🔍 Detailed Performance Analysis on Mock Data")
	t.Logf("============================================")
	t.Logf("Dataset: %d rows, %d columns", len(rows), len(columns))

	// Extract and analyze data
	protocolData := extractColumnData(rows, "protocol")
	actionData := extractColumnData(rows, "action")
	ruleCategoryData := extractColumnData(rows, "rule_category")
	sourceCountryData := extractColumnData(rows, "source_country")

	// Generate V2 indexes (our best performer)
	generatorV2 := NewV2Generator(tempDir)

	indexStart := time.Now()
	generatorV2.GenerateV2("protocol", protocolData)
	generatorV2.GenerateV2("action", actionData)
	generatorV2.GenerateV2("rule_category", ruleCategoryData)
	generatorV2.GenerateV2("source_country", sourceCountryData)
	indexTime := time.Since(indexStart)

	t.Logf("📦 Index Generation: %v", indexTime)

	// Create engine
	engine := NewV2QueryEngine(tempDir)
	defer engine.Close()

	// Test queries with detailed timing
	testQueries := []struct {
		name     string
		column   string
		value    string
		expected bool // whether we expect results
	}{
		{"TCP Protocol", "protocol", "TCP", true},
		{"UDP Protocol", "protocol", "UDP", true},
		{"ICMP Protocol", "protocol", "ICMP", true},
		{"Block Action", "action", "Block", true},
		{"Allow Action", "action", "Allow", true},
		{"Web Filtering", "rule_category", "Web Filtering", true},
		{"Intrusion Detection", "rule_category", "Intrusion Detection", true},
		{"China Source", "source_country", "China", true},
		{"United States Source", "source_country", "United States", true},
		{"Russia Source", "source_country", "Russia", true},
		{"Nonexistent Protocol", "protocol", "NONEXISTENT", false},
		{"Nonexistent Action", "action", "NONEXISTENT", false},
		{"Nonexistent Country", "source_country", "NONEXISTENT", false},
	}

	t.Logf("\n🚀 Query Performance Results:")
	t.Logf("%-25s %-15s %-10s %-15s %-10s", "Query", "Column", "Results", "Time", "Status")
	t.Logf("%-25s %-15s %-10s %-15s %-10s", "-----", "------", "-------", "----", "------")

	var totalTime time.Duration
	var positiveQueries, negativeQueries int
	var positiveTime, negativeTime time.Duration

	for _, query := range testQueries {
		// Warm up
		for i := 0; i < 5; i++ {
			engine.SearchV2(query.column, query.value)
		}

		// Measure performance over multiple runs
		measurements := make([]time.Duration, 100)
		for i := 0; i < 100; i++ {
			start := time.Now()
			results, err := engine.SearchV2(query.column, query.value)
			measurements[i] = time.Since(start)

			if err != nil {
				t.Errorf("Query failed: %v", err)
				continue
			}

			// Verify expectations
			hasResults := len(results) > 0
			if hasResults != query.expected {
				if query.expected {
					t.Logf("Warning: Expected results for %s but got none", query.name)
				}
			}
		}

		// Calculate statistics
		var sum time.Duration
		min := measurements[0]
		max := measurements[0]

		for _, d := range measurements {
			sum += d
			if d < min {
				min = d
			}
			if d > max {
				max = d
			}
		}

		avg := sum / time.Duration(len(measurements))
		totalTime += avg

		// Track positive vs negative queries
		if query.expected {
			positiveQueries++
			positiveTime += avg
		} else {
			negativeQueries++
			negativeTime += avg
		}

		// Get actual result count for display
		results, _ := engine.SearchV2(query.column, query.value)

		status := "✅"
		if avg > 5*time.Microsecond {
			status = "⚠️"
		}
		if avg > 10*time.Microsecond {
			status = "❌"
		}

		t.Logf("%-25s %-15s %-10d %-15s %-10s",
			query.name,
			query.column,
			len(results),
			fmt.Sprintf("%.2fµs", float64(avg.Nanoseconds())/1000.0),
			status)
	}

	// Summary statistics
	avgPositive := positiveTime / time.Duration(positiveQueries)
	avgNegative := negativeTime / time.Duration(negativeQueries)

	t.Logf("\n📈 Performance Summary:")
	t.Logf("  Average positive query: %.2fµs", float64(avgPositive.Nanoseconds())/1000.0)
	t.Logf("  Average negative query: %.2fµs", float64(avgNegative.Nanoseconds())/1000.0)
	t.Logf("  Overall average:        %.2fµs", float64(totalTime.Nanoseconds())/float64(len(testQueries))/1000.0)

	t.Logf("\n🎯 Performance Targets:")
	if avgPositive <= 5*time.Microsecond {
		t.Logf("  ✅ Positive queries < 5µs: ACHIEVED (%.2fµs)", float64(avgPositive.Nanoseconds())/1000.0)
	} else {
		t.Logf("  ❌ Positive queries < 5µs: MISSED (%.2fµs)", float64(avgPositive.Nanoseconds())/1000.0)
	}

	if avgNegative <= 1*time.Microsecond {
		t.Logf("  ✅ Negative queries < 1µs: ACHIEVED (%.2fµs)", float64(avgNegative.Nanoseconds())/1000.0)
	} else {
		t.Logf("  ❌ Negative queries < 1µs: MISSED (%.2fµs)", float64(avgNegative.Nanoseconds())/1000.0)
	}
}

// Helper function to get unique values from records
func getUniqueValues(records []Record) map[string]bool {
	unique := make(map[string]bool)
	for _, record := range records {
		unique[record.Value] = true
	}
	return unique
}
