package main

import (
	"os"
	"testing"
	"time"
)

func TestMicrosecondPerformance(t *testing.T) {
	// Create temporary directory
	tempDir := "/tmp/ultrafast_microsecond_test"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Generate test data optimized for performance testing
	records := make([]Record, 1000)
	values := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS"}

	for i := 0; i < 1000; i++ {
		records[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      values[i%len(values)],
		}
	}

	// Generate V2 index
	generator := NewV2Generator(tempDir)
	start := time.Now()
	err := generator.GenerateV2("protocol", records)
	generateTime := time.Since(start)

	if err != nil {
		t.Fatalf("Failed to generate V2 index: %v", err)
	}

	t.Logf("Index generation time: %v", generateTime)

	// Test V2 engine
	engine := NewV2QueryEngine(tempDir)
	defer engine.Close()

	// Warm up the engine (important for accurate measurements)
	for i := 0; i < 10; i++ {
		engine.SearchV2("protocol", "TCP")
	}

	// Measure single search performance
	measurements := make([]time.Duration, 1000)

	for i := 0; i < 1000; i++ {
		start := time.Now()
		_, err := engine.SearchV2("protocol", "TCP")
		measurements[i] = time.Since(start)

		if err != nil {
			t.Fatalf("Search failed: %v", err)
		}
	}

	// Calculate statistics
	var total time.Duration
	min := measurements[0]
	max := measurements[0]

	for _, duration := range measurements {
		total += duration
		if duration < min {
			min = duration
		}
		if duration > max {
			max = duration
		}
	}

	avg := total / time.Duration(len(measurements))

	t.Logf("Performance Statistics (1000 searches):")
	t.Logf("  Average: %v (%.2f µs)", avg, float64(avg.Nanoseconds())/1000.0)
	t.Logf("  Minimum: %v (%.2f µs)", min, float64(min.Nanoseconds())/1000.0)
	t.Logf("  Maximum: %v (%.2f µs)", max, float64(max.Nanoseconds())/1000.0)

	// Test negative searches (bloom filter)
	negativeStart := time.Now()
	for i := 0; i < 1000; i++ {
		engine.SearchV2("protocol", "NONEXISTENT")
	}
	negativeTotal := time.Since(negativeStart)
	negativeAvg := negativeTotal / 1000

	t.Logf("Negative search average: %v (%.2f µs)", negativeAvg, float64(negativeAvg.Nanoseconds())/1000.0)

	// Performance assertions
	if avg > 5*time.Microsecond {
		t.Errorf("Average search time too slow: %v (target: < 5µs)", avg)
	}

	if negativeAvg > 1*time.Microsecond {
		t.Errorf("Negative search time too slow: %v (target: < 1µs)", negativeAvg)
	}

	// Success metrics
	if avg <= 5*time.Microsecond {
		t.Logf("✅ ACHIEVED MICROSECOND-LEVEL PERFORMANCE!")
		t.Logf("   Average search time: %.2f µs", float64(avg.Nanoseconds())/1000.0)
	}

	if negativeAvg <= 1*time.Microsecond {
		t.Logf("✅ ACHIEVED SUB-MICROSECOND NEGATIVE LOOKUPS!")
		t.Logf("   Negative search time: %.2f µs", float64(negativeAvg.Nanoseconds())/1000.0)
	}
}

func BenchmarkMicrosecondComparison(b *testing.B) {
	// Setup
	tempDirV1 := "/tmp/ultrafast_micro_v1"
	tempDirV2 := "/tmp/ultrafast_micro_v2"
	tempDirVec := "/tmp/ultrafast_micro_vec"

	os.MkdirAll(tempDirV1, 0755)
	os.MkdirAll(tempDirV2, 0755)
	os.MkdirAll(tempDirVec, 0755)

	defer os.RemoveAll(tempDirV1)
	defer os.RemoveAll(tempDirV2)
	defer os.RemoveAll(tempDirVec)

	// Generate test data
	records := make([]Record, 10000)
	protocols := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS", "FTP", "SSH", "DNS"}

	for i := 0; i < 10000; i++ {
		records[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      protocols[i%len(protocols)],
		}
	}

	// Generate indexes
	generatorV1 := NewGenerator(tempDirV1)
	generatorV1.Generate("protocol", records)

	generatorV2 := NewV2Generator(tempDirV2)
	generatorV2.GenerateV2("protocol", records)

	// Create engines
	engineV1 := NewQueryEngine(tempDirV1)
	defer engineV1.Close()

	engineV2 := NewV2QueryEngine(tempDirV2)
	defer engineV2.Close()

	vectorizedEngine := NewVectorizedQueryEngine(tempDirV2)
	defer vectorizedEngine.Close()

	// Warm up
	for i := 0; i < 100; i++ {
		engineV1.Search("protocol", "TCP")
		engineV2.SearchV2("protocol", "TCP")
	}

	b.Run("V1_SingleSearch", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV1.Search("protocol", "TCP")
		}
	})

	b.Run("V2_SingleSearch", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("protocol", "TCP")
		}
	})

	b.Run("V2_NegativeSearch", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			engineV2.SearchV2("protocol", "NONEXISTENT")
		}
	})

	b.Run("Vectorized_BatchSearch", func(b *testing.B) {
		values := []string{"TCP", "UDP", "ICMP"}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			vectorizedEngine.BatchSearch("protocol", values)
		}
	})
}

func TestVectorizedPerformance(t *testing.T) {
	// Create temporary directory
	tempDir := "/tmp/ultrafast_vectorized_test"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Generate test data
	records := make([]Record, 5000)
	protocols := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS"}

	for i := 0; i < 5000; i++ {
		records[i] = Record{
			LineNumber: uint32(i + 1),
			Value:      protocols[i%len(protocols)],
		}
	}

	// Generate V2 index
	generator := NewV2Generator(tempDir)
	err := generator.GenerateV2("protocol", records)
	if err != nil {
		t.Fatalf("Failed to generate V2 index: %v", err)
	}

	// Test vectorized engine
	vectorizedEngine := NewVectorizedQueryEngine(tempDir)
	defer vectorizedEngine.Close()

	// Test batch search
	searchValues := []string{"TCP", "UDP", "ICMP", "HTTP", "HTTPS"}

	start := time.Now()
	results, err := vectorizedEngine.BatchSearch("protocol", searchValues)
	batchTime := time.Since(start)

	if err != nil {
		t.Fatalf("Batch search failed: %v", err)
	}

	t.Logf("Batch search time for %d values: %v", len(searchValues), batchTime)
	t.Logf("Average time per search: %v", batchTime/time.Duration(len(searchValues)))

	// Verify results
	totalResults := 0
	for value, lineNumbers := range results {
		t.Logf("  %s: %d results", value, len(lineNumbers))
		totalResults += len(lineNumbers)
	}

	t.Logf("Total results: %d", totalResults)

	if totalResults != len(records) {
		t.Errorf("Expected %d total results, got %d", len(records), totalResults)
	}
}

func TestPerformanceComparison(t *testing.T) {
	// Performance comparison summary
	t.Log("🚀 ULTRAFAST PERFORMANCE SUMMARY")
	t.Log("================================")

	// Run a quick performance test
	tempDir := "/tmp/ultrafast_summary_test"
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Small dataset for quick testing
	records := []Record{
		{LineNumber: 1, Value: "TCP"},
		{LineNumber: 2, Value: "UDP"},
		{LineNumber: 3, Value: "TCP"},
		{LineNumber: 4, Value: "ICMP"},
	}

	// V1 Performance
	generatorV1 := NewGenerator(tempDir)
	generatorV1.Generate("protocol", records)
	engineV1 := NewQueryEngine(tempDir)
	defer engineV1.Close()

	start := time.Now()
	for i := 0; i < 1000; i++ {
		engineV1.Search("protocol", "TCP")
	}
	v1Time := time.Since(start) / 1000

	// V2 Performance
	generatorV2 := NewV2Generator(tempDir)
	generatorV2.GenerateV2("protocol", records)
	engineV2 := NewV2QueryEngine(tempDir)
	defer engineV2.Close()

	start = time.Now()
	for i := 0; i < 1000; i++ {
		engineV2.SearchV2("protocol", "TCP")
	}
	v2Time := time.Since(start) / 1000

	// Negative search performance
	start = time.Now()
	for i := 0; i < 1000; i++ {
		engineV2.SearchV2("protocol", "NONEXISTENT")
	}
	negativeTime := time.Since(start) / 1000

	t.Logf("📊 Performance Results:")
	t.Logf("  V1 Format:        %.2f µs", float64(v1Time.Nanoseconds())/1000.0)
	t.Logf("  V2 Format:        %.2f µs", float64(v2Time.Nanoseconds())/1000.0)
	t.Logf("  V2 Negative:      %.2f µs", float64(negativeTime.Nanoseconds())/1000.0)
	t.Logf("")
	t.Logf("🎯 Performance Targets:")

	if v2Time <= 5*time.Microsecond {
		t.Logf("  ✅ Positive searches: < 5µs (ACHIEVED: %.2f µs)", float64(v2Time.Nanoseconds())/1000.0)
	} else {
		t.Logf("  ❌ Positive searches: < 5µs (Current: %.2f µs)", float64(v2Time.Nanoseconds())/1000.0)
	}

	if negativeTime <= 1*time.Microsecond {
		t.Logf("  ✅ Negative searches: < 1µs (ACHIEVED: %.2f µs)", float64(negativeTime.Nanoseconds())/1000.0)
	} else {
		t.Logf("  ❌ Negative searches: < 1µs (Current: %.2f µs)", float64(negativeTime.Nanoseconds())/1000.0)
	}

	t.Logf("")
	t.Logf("🔧 Optimizations Implemented:")
	t.Logf("  ✅ RoaringBitmap compression")
	t.Logf("  ✅ SIMD-optimized hash functions")
	t.Logf("  ✅ Bloom filters for negative lookups")
	t.Logf("  ✅ Delta compression")
	t.Logf("  ✅ Memory-mapped I/O")
	t.Logf("  ✅ Cache-aligned data structures")
	t.Logf("  ✅ Vectorized query execution")
	t.Logf("  ✅ Parallel batch processing")
}
