package main

import (
	"os"
	"testing"
	"time"
)

// TestData represents test data for multi-column queries
type TestData struct {
	Users []RowRecord
	Logs  []RowRecord
}

// setupTestData creates test data for multi-column query testing
func setupTestData() *TestData {
	users := []RowRecord{
		{LineNumber: 1, Values: map[string]string{"id": "1", "username": "john_doe", "department": "IT", "status": "active", "email": "<EMAIL>"}},
		{LineNumber: 2, Values: map[string]string{"id": "2", "username": "jane_smith", "department": "HR", "status": "active", "email": "<EMAIL>"}},
		{LineNumber: 3, Values: map[string]string{"id": "3", "username": "bob_wilson", "department": "IT", "status": "inactive", "email": "<EMAIL>"}},
		{LineNumber: 4, Values: map[string]string{"id": "4", "username": "alice_brown", "department": "Finance", "status": "active", "email": "<EMAIL>"}},
		{LineNumber: 5, Values: map[string]string{"id": "5", "username": "charlie_davis", "department": "IT", "status": "active", "email": "<EMAIL>"}},
		{LineNumber: 6, Values: map[string]string{"id": "6", "username": "diana_miller", "department": "HR", "status": "inactive", "email": "<EMAIL>"}},
		{LineNumber: 7, Values: map[string]string{"id": "7", "username": "eve_garcia", "department": "Finance", "status": "active", "email": "<EMAIL>"}},
		{LineNumber: 8, Values: map[string]string{"id": "8", "username": "frank_martinez", "department": "IT", "status": "active", "email": "<EMAIL>"}},
	}

	logs := []RowRecord{
		{LineNumber: 1, Values: map[string]string{"id": "1", "user_id": "1", "action": "login", "timestamp": "2024-01-01T10:00:00Z", "ip": "*************"}},
		{LineNumber: 2, Values: map[string]string{"id": "2", "user_id": "2", "action": "logout", "timestamp": "2024-01-01T11:00:00Z", "ip": "*************"}},
		{LineNumber: 3, Values: map[string]string{"id": "3", "user_id": "1", "action": "file_access", "timestamp": "2024-01-01T12:00:00Z", "ip": "*************"}},
		{LineNumber: 4, Values: map[string]string{"id": "4", "user_id": "3", "action": "login", "timestamp": "2024-01-01T13:00:00Z", "ip": "*************"}},
		{LineNumber: 5, Values: map[string]string{"id": "5", "user_id": "4", "action": "login", "timestamp": "2024-01-01T14:00:00Z", "ip": "*************"}},
	}

	return &TestData{
		Users: users,
		Logs:  logs,
	}
}

// setupTestEnvironment creates temporary directories and indexes for testing
func setupTestEnvironment(t *testing.T, data *TestData) (string, func()) {
	// Create temporary directory
	tempDir, err := os.MkdirTemp("", "ultrafast_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	// Generate indexes for users table
	generator := NewGenerator(tempDir)
	userColumns := []string{"id", "username", "department", "status", "email"}

	for _, column := range userColumns {
		columnData := extractColumnData(data.Users, column)
		if err := generator.Generate(column, columnData); err != nil {
			t.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	// Generate row store for users
	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("users", data.Users); err != nil {
		t.Fatalf("Failed to generate row store for users: %v", err)
	}

	// Generate indexes for logs table
	logColumns := []string{"id", "user_id", "action", "timestamp", "ip"}

	for _, column := range logColumns {
		columnData := extractColumnData(data.Logs, column)
		if err := generator.Generate(column, columnData); err != nil {
			t.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	// Generate row store for logs
	if err := rowStore.GenerateRowStore("logs", data.Logs); err != nil {
		t.Fatalf("Failed to generate row store for logs: %v", err)
	}

	// Cleanup function
	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempDir, cleanup
}

func TestBasicMultiColumnQuery(t *testing.T) {
	data := setupTestData()
	tempDir, cleanup := setupTestEnvironment(t, data)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test AND query: department=IT AND status=active
	filter := And(Eq("department", "IT"), Eq("status", "active"))
	query := &QueryRequest{
		Filter:     filter,
		SelectCols: []string{"username", "email"},
	}

	result, err := engine.ExecuteQuery("users", query)
	if err != nil {
		t.Fatalf("Query execution failed: %v", err)
	}

	// Should return john_doe, charlie_davis, frank_martinez
	expectedCount := 3
	if len(result.Rows) != expectedCount {
		t.Errorf("Expected %d results, got %d", expectedCount, len(result.Rows))
	}

	// Verify specific results
	usernames := make(map[string]bool)
	for _, row := range result.Rows {
		usernames[row["username"]] = true
	}

	expectedUsers := []string{"john_doe", "charlie_davis", "frank_martinez"}
	for _, user := range expectedUsers {
		if !usernames[user] {
			t.Errorf("Expected user %s not found in results", user)
		}
	}
}

func TestORQuery(t *testing.T) {
	data := setupTestData()
	tempDir, cleanup := setupTestEnvironment(t, data)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test OR query: department=HR OR department=Finance
	filter := Or(Eq("department", "HR"), Eq("department", "Finance"))
	query := &QueryRequest{
		Filter:     filter,
		SelectCols: []string{"username", "department"},
	}

	result, err := engine.ExecuteQuery("users", query)
	if err != nil {
		t.Fatalf("Query execution failed: %v", err)
	}

	// Should return jane_smith, alice_brown, diana_miller, eve_garcia
	expectedCount := 4
	if len(result.Rows) != expectedCount {
		t.Errorf("Expected %d results, got %d", expectedCount, len(result.Rows))
	}
}

func TestComplexQuery(t *testing.T) {
	data := setupTestData()
	tempDir, cleanup := setupTestEnvironment(t, data)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test complex query: (department=IT AND status=active) OR (department=Finance AND status=active)
	filter := Or(
		And(Eq("department", "IT"), Eq("status", "active")),
		And(Eq("department", "Finance"), Eq("status", "active")),
	)

	query := &QueryRequest{
		Filter:     filter,
		SelectCols: []string{"username", "department", "status"},
	}

	result, err := engine.ExecuteQuery("users", query)
	if err != nil {
		t.Fatalf("Query execution failed: %v", err)
	}

	// Should return active users from IT and Finance departments
	expectedCount := 5 // john_doe, charlie_davis, frank_martinez, alice_brown, eve_garcia
	if len(result.Rows) != expectedCount {
		t.Errorf("Expected %d results, got %d", expectedCount, len(result.Rows))
	}
}

func TestQueryParser(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{
			input:    "username=john_doe",
			expected: "single condition",
		},
		{
			input:    "department=IT AND status=active",
			expected: "AND condition",
		},
		{
			input:    "department=HR OR department=Finance",
			expected: "OR condition",
		},
		{
			input:    "(department=IT AND status=active) OR department=HR",
			expected: "complex condition with parentheses",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.expected, func(t *testing.T) {
			filter, err := ParseFilterExpression(tc.input)
			if err != nil {
				t.Errorf("Failed to parse filter expression '%s': %v", tc.input, err)
				return
			}

			if filter == nil {
				t.Errorf("Parsed filter is nil for input '%s'", tc.input)
			}
		})
	}
}

func TestBitmapOperations(t *testing.T) {
	// Test RoaringBitmap operations
	bitmap1 := NewRoaringBitmap()
	bitmap2 := NewRoaringBitmap()

	// Set some bits
	bitmap1.Add(1)
	bitmap1.Add(5)
	bitmap1.Add(10)

	bitmap2.Add(5)
	bitmap2.Add(10)
	bitmap2.Add(15)

	// Test AND operation
	andResult := bitmap1.And(bitmap2)
	andBits := andResult.ToSlice()
	expectedAnd := []uint32{5, 10}

	if len(andBits) != len(expectedAnd) {
		t.Errorf("AND operation: expected %d bits, got %d", len(expectedAnd), len(andBits))
	}

	for i, bit := range expectedAnd {
		if i >= len(andBits) || andBits[i] != bit {
			t.Errorf("AND operation: expected bit %d, got %d", bit, andBits[i])
		}
	}

	// Test OR operation
	orResult := bitmap1.Or(bitmap2)
	orBits := orResult.ToSlice()
	expectedOr := []uint32{1, 5, 10, 15}

	if len(orBits) != len(expectedOr) {
		t.Errorf("OR operation: expected %d bits, got %d", len(expectedOr), len(orBits))
	}

	for i, bit := range expectedOr {
		if i >= len(orBits) || orBits[i] != bit {
			t.Errorf("OR operation: expected bit %d, got %d", bit, orBits[i])
		}
	}
}

func TestRowStoreOperations(t *testing.T) {
	data := setupTestData()
	tempDir, cleanup := setupTestEnvironment(t, data)
	defer cleanup()

	rowStore := NewRowStore(tempDir)
	defer rowStore.Close()

	// Test GetRow
	row, err := rowStore.GetRow("users", 1)
	if err != nil {
		t.Fatalf("Failed to get row: %v", err)
	}

	if row["username"] != "john_doe" {
		t.Errorf("Expected username 'john_doe', got '%s'", row["username"])
	}

	// Test GetRowsWithProjection
	lineNumbers := []uint32{1, 2, 3}
	selectCols := []string{"username", "department"}

	rows, err := rowStore.GetRowsWithProjection("users", lineNumbers, selectCols)
	if err != nil {
		t.Fatalf("Failed to get rows with projection: %v", err)
	}

	if len(rows) != 3 {
		t.Errorf("Expected 3 rows, got %d", len(rows))
	}

	// Verify projection worked
	for _, row := range rows {
		if len(row) != 2 {
			t.Errorf("Expected 2 columns in projected row, got %d", len(row))
		}
		if _, exists := row["username"]; !exists {
			t.Error("Expected 'username' column in projected row")
		}
		if _, exists := row["department"]; !exists {
			t.Error("Expected 'department' column in projected row")
		}
	}
}

func TestPerformanceComparison(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	data := setupTestData()
	tempDir, cleanup := setupTestEnvironment(t, data)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test regular execution vs bitmap execution
	filter := And(Eq("department", "IT"), Eq("status", "active"))
	query := &QueryRequest{
		Filter:     filter,
		SelectCols: []string{"username", "email"},
	}

	// Regular execution
	start := time.Now()
	result1, err := engine.ExecuteQuery("users", query)
	regularTime := time.Since(start)
	if err != nil {
		t.Fatalf("Regular query execution failed: %v", err)
	}

	// Bitmap execution
	start = time.Now()
	result2, err := engine.ExecuteQueryWithBitmaps("users", query, 1000)
	bitmapTime := time.Since(start)
	if err != nil {
		t.Fatalf("Bitmap query execution failed: %v", err)
	}

	// Results should be identical
	if len(result1.Rows) != len(result2.Rows) {
		t.Errorf("Result count mismatch: regular=%d, bitmap=%d", len(result1.Rows), len(result2.Rows))
	}

	t.Logf("Performance comparison:")
	t.Logf("  Regular execution: %v", regularTime)
	t.Logf("  Bitmap execution:  %v", bitmapTime)
	t.Logf("  Results: %d rows", len(result1.Rows))
}
