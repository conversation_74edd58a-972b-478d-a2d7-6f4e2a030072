package main

import (
	"fmt"
	"os"
	"testing"
	"time"
)

// BenchmarkMockDataRealWorld performs comprehensive benchmarks on mock_data.csv
// simulating real-world network security analysis scenarios
func BenchmarkMockDataRealWorld(b *testing.B) {
	// Check if mock_data.csv exists
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		b.<PERSON>("mock_data.csv not found, skipping real-world benchmark")
	}

	// Setup benchmark environment
	tempDir, cleanup := setupMockDataBenchmark(b)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Get data size for reporting
	rows, _, _ := readFullCSV("mock_data.csv")
	dataSize := len(rows)
	b.Logf("Benchmarking with %d network security records", dataSize)

	// Benchmark Suite: Network Security Analysis Scenarios

	// Scenario 1: Protocol-based traffic analysis
	b.Run("ProtocolAnalysis", func(b *testing.B) {
		protocols := []string{"TCP", "UDP", "ICMP"}

		for _, protocol := range protocols {
			b.Run(protocol, func(b *testing.B) {
				filter := Eq("protocol", protocol)
				query := &QueryRequest{
					Filter:     filter,
					SelectCols: []string{"timestamp", "source_ip", "destination_ip", "action", "rule_name"},
				}

				b.ResetTimer()
				var totalMatches int
				for i := 0; i < b.N; i++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					totalMatches = result.TotalMatches
				}
				b.ReportMetric(float64(totalMatches), "matches")
				b.ReportMetric(float64(totalMatches)/float64(dataSize)*100, "match_percentage")
			})
		}
	})

	// Scenario 2: Security action analysis
	b.Run("SecurityActionAnalysis", func(b *testing.B) {
		actions := []string{"Allow", "Block"}

		for _, action := range actions {
			b.Run(action, func(b *testing.B) {
				filter := Eq("action", action)
				query := &QueryRequest{
					Filter:     filter,
					SelectCols: []string{"protocol", "source_country", "destination_country", "rule_category"},
				}

				b.ResetTimer()
				var totalMatches int
				for i := 0; i < b.N; i++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					totalMatches = result.TotalMatches
				}
				b.ReportMetric(float64(totalMatches), "matches")
			})
		}
	})

	// Scenario 3: Rule category analysis
	b.Run("RuleCategoryAnalysis", func(b *testing.B) {
		categories := []string{"Web Filtering", "Intrusion Detection", "Application Control"}

		for _, category := range categories {
			b.Run(fmt.Sprintf("Category_%s", category), func(b *testing.B) {
				filter := Eq("rule_category", category)
				query := &QueryRequest{
					Filter:     filter,
					SelectCols: []string{"protocol", "action", "source_country", "destination_country"},
				}

				b.ResetTimer()
				var totalMatches int
				for i := 0; i < b.N; i++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					totalMatches = result.TotalMatches
				}
				b.ReportMetric(float64(totalMatches), "matches")
			})
		}
	})

	// Scenario 4: Geographic threat analysis
	b.Run("GeographicThreatAnalysis", func(b *testing.B) {
		countries := []string{"China", "Russia", "United States"}

		for _, country := range countries {
			b.Run(fmt.Sprintf("Source_%s", country), func(b *testing.B) {
				filter := Eq("source_country", country)
				query := &QueryRequest{
					Filter:     filter,
					SelectCols: []string{"protocol", "action", "destination_country", "rule_category"},
				}

				b.ResetTimer()
				var totalMatches int
				for i := 0; i < b.N; i++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					totalMatches = result.TotalMatches
				}
				b.ReportMetric(float64(totalMatches), "matches")
			})
		}
	})

	// Scenario 5: Complex security analysis queries
	b.Run("ComplexSecurityQueries", func(b *testing.B) {

		// Query 1: Blocked TCP traffic
		b.Run("BlockedTCPTraffic", func(b *testing.B) {
			filter := And(Eq("protocol", "TCP"), Eq("action", "Block"))
			query := &QueryRequest{
				Filter:     filter,
				SelectCols: []string{"timestamp", "source_ip", "destination_ip", "rule_name", "rule_category"},
			}

			b.ResetTimer()
			var totalMatches int
			for i := 0; i < b.N; i++ {
				result, err := engine.ExecuteQuery("network_logs", query)
				if err != nil {
					b.Fatalf("Query failed: %v", err)
				}
				totalMatches = result.TotalMatches
			}
			b.ReportMetric(float64(totalMatches), "matches")
		})

		// Query 2: Web filtering blocks
		b.Run("WebFilteringBlocks", func(b *testing.B) {
			filter := And(Eq("rule_category", "Web Filtering"), Eq("action", "Block"))
			query := &QueryRequest{
				Filter:     filter,
				SelectCols: []string{"protocol", "source_country", "destination_country", "rule_name"},
			}

			b.ResetTimer()
			var totalMatches int
			for i := 0; i < b.N; i++ {
				result, err := engine.ExecuteQuery("network_logs", query)
				if err != nil {
					b.Fatalf("Query failed: %v", err)
				}
				totalMatches = result.TotalMatches
			}
			b.ReportMetric(float64(totalMatches), "matches")
		})

		// Query 3: Multi-protocol analysis
		b.Run("MultiProtocolAnalysis", func(b *testing.B) {
			filter := Or(Eq("protocol", "TCP"), Eq("protocol", "UDP"))
			query := &QueryRequest{
				Filter:     filter,
				SelectCols: []string{"protocol", "action", "rule_category"},
			}

			b.ResetTimer()
			var totalMatches int
			for i := 0; i < b.N; i++ {
				result, err := engine.ExecuteQuery("network_logs", query)
				if err != nil {
					b.Fatalf("Query failed: %v", err)
				}
				totalMatches = result.TotalMatches
			}
			b.ReportMetric(float64(totalMatches), "matches")
		})

		// Query 4: Complex nested security analysis
		b.Run("NestedSecurityAnalysis", func(b *testing.B) {
			filter := Or(
				And(Eq("rule_category", "Intrusion Detection"), Eq("action", "Block")),
				And(Eq("rule_category", "Web Filtering"), Eq("source_country", "China")),
			)
			query := &QueryRequest{
				Filter:     filter,
				SelectCols: []string{"protocol", "action", "rule_category", "source_country", "destination_country"},
			}

			b.ResetTimer()
			var totalMatches int
			for i := 0; i < b.N; i++ {
				result, err := engine.ExecuteQuery("network_logs", query)
				if err != nil {
					b.Fatalf("Query failed: %v", err)
				}
				totalMatches = result.TotalMatches
			}
			b.ReportMetric(float64(totalMatches), "matches")
		})
	})

	// Scenario 6: Column projection performance
	b.Run("ColumnProjectionPerformance", func(b *testing.B) {
		filter := Eq("protocol", "TCP")

		projections := []struct {
			name string
			cols []string
		}{
			{"Minimal", []string{"protocol"}},
			{"Basic", []string{"protocol", "action", "source_ip"}},
			{"Standard", []string{"protocol", "action", "source_ip", "destination_ip", "rule_name"}},
			{"Extended", []string{"protocol", "action", "source_ip", "destination_ip", "rule_name", "rule_category", "source_country", "destination_country"}},
		}

		for _, proj := range projections {
			b.Run(proj.name, func(b *testing.B) {
				query := &QueryRequest{
					Filter:     filter,
					SelectCols: proj.cols,
				}

				b.ResetTimer()
				for i := 0; i < b.N; i++ {
					result, err := engine.ExecuteQuery("network_logs", query)
					if err != nil {
						b.Fatalf("Query failed: %v", err)
					}
					_ = result
				}
				b.ReportMetric(float64(len(proj.cols)), "columns_projected")
			})
		}
	})

	// Scenario 7: Large result set handling
	b.Run("LargeResultSetHandling", func(b *testing.B) {
		// Query that returns many results to test bitmap optimization
		filter := Or(
			Or(Eq("protocol", "TCP"), Eq("protocol", "UDP")),
			Eq("protocol", "ICMP"),
		)
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action"},
		}

		b.ResetTimer()
		var totalMatches int
		for i := 0; i < b.N; i++ {
			result, err := engine.ExecuteQuery("network_logs", query)
			if err != nil {
				b.Fatalf("Query failed: %v", err)
			}
			totalMatches = result.TotalMatches
		}
		b.ReportMetric(float64(totalMatches), "matches")
		b.ReportMetric(float64(totalMatches)/float64(dataSize)*100, "match_percentage")
	})
}

// BenchmarkMockDataScalability tests performance characteristics at different scales
func BenchmarkMockDataScalability(b *testing.B) {
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		b.Skip("mock_data.csv not found, skipping scalability benchmark")
	}

	tempDir, cleanup := setupMockDataBenchmark(b)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Test different query complexities
	queries := []struct {
		name   string
		filter *FilterExpression
	}{
		{"Simple", Eq("protocol", "TCP")},
		{"AND", And(Eq("protocol", "TCP"), Eq("action", "Block"))},
		{"OR", Or(Eq("protocol", "TCP"), Eq("protocol", "UDP"))},
		{"Complex", Or(
			And(Eq("protocol", "TCP"), Eq("action", "Block")),
			And(Eq("rule_category", "Web Filtering"), Eq("source_country", "China")),
		)},
	}

	for _, q := range queries {
		b.Run(q.name, func(b *testing.B) {
			query := &QueryRequest{
				Filter:     q.filter,
				SelectCols: []string{"protocol", "action", "source_country", "rule_category"},
			}

			b.ResetTimer()
			start := time.Now()
			var totalMatches int

			for i := 0; i < b.N; i++ {
				result, err := engine.ExecuteQuery("network_logs", query)
				if err != nil {
					b.Fatalf("Query failed: %v", err)
				}
				totalMatches = result.TotalMatches
			}

			elapsed := time.Since(start)
			b.ReportMetric(float64(totalMatches), "matches")
			b.ReportMetric(float64(elapsed.Nanoseconds())/float64(b.N), "ns_per_query")
		})
	}
}

// setupMockDataBenchmark sets up the benchmark environment
func setupMockDataBenchmark(b *testing.B) (string, func()) {
	tempDir, err := os.MkdirTemp("", "ultrafast_mock_benchmark_*")
	if err != nil {
		b.Fatalf("Failed to create temp directory: %v", err)
	}

	// Read and index data
	rows, _, err := readFullCSV("mock_data.csv")
	if err != nil {
		b.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	generator := NewGenerator(tempDir)

	// Index key columns for network security analysis
	keyColumns := []string{
		"protocol", "action", "rule_name", "rule_category",
		"source_country", "destination_country",
	}

	for _, column := range keyColumns {
		columnData := extractColumnData(rows, column)
		if len(columnData) == 0 {
			continue // Skip empty columns
		}
		if err := generator.Generate(column, columnData); err != nil {
			b.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	// Generate row store
	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("network_logs", rows); err != nil {
		b.Fatalf("Failed to generate row store: %v", err)
	}

	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempDir, cleanup
}

// setupMockDataBenchmarkForTest sets up the benchmark environment for testing
func setupMockDataBenchmarkForTest(t *testing.T) (string, func()) {
	tempDir, err := os.MkdirTemp("", "ultrafast_mock_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	// Read and index data
	rows, _, err := readFullCSV("mock_data.csv")
	if err != nil {
		t.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	generator := NewGenerator(tempDir)

	// Index key columns for network security analysis
	keyColumns := []string{
		"protocol", "action", "rule_name", "rule_category",
		"source_country", "destination_country",
	}

	for _, column := range keyColumns {
		columnData := extractColumnData(rows, column)
		if len(columnData) == 0 {
			continue // Skip empty columns
		}
		if err := generator.Generate(column, columnData); err != nil {
			t.Fatalf("Failed to generate index for column %s: %v", column, err)
		}
	}

	// Generate row store
	rowStore := NewRowStore(tempDir)
	if err := rowStore.GenerateRowStore("network_logs", rows); err != nil {
		t.Fatalf("Failed to generate row store: %v", err)
	}

	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempDir, cleanup
}

// TestMockDataQueryAccuracy verifies query result accuracy
func TestMockDataQueryAccuracy(t *testing.T) {
	if _, err := os.Stat("mock_data.csv"); os.IsNotExist(err) {
		t.Skip("mock_data.csv not found, skipping accuracy test")
	}

	tempDir, cleanup := setupMockDataBenchmarkForTest(t)
	defer cleanup()

	engine := NewQueryEngine(tempDir)
	defer engine.Close()

	// Read original data for verification
	rows, _, err := readFullCSV("mock_data.csv")
	if err != nil {
		t.Fatalf("Failed to read mock_data.csv: %v", err)
	}

	// Test 1: Verify TCP count
	t.Run("TCPCount", func(t *testing.T) {
		filter := Eq("protocol", "TCP")
		query := &QueryRequest{Filter: filter, SelectCols: []string{"protocol"}}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query failed: %v", err)
		}

		// Count TCP in original data
		tcpCount := 0
		for _, row := range rows {
			if row.Values["protocol"] == "TCP" {
				tcpCount++
			}
		}

		if result.TotalMatches != tcpCount {
			t.Errorf("TCP count mismatch: query=%d, actual=%d", result.TotalMatches, tcpCount)
		}
		t.Logf("TCP records: %d (verified)", tcpCount)
	})

	// Test 2: Verify complex query accuracy
	t.Run("ComplexQueryAccuracy", func(t *testing.T) {
		filter := And(Eq("protocol", "TCP"), Eq("action", "Block"))
		query := &QueryRequest{
			Filter:     filter,
			SelectCols: []string{"protocol", "action"},
		}

		result, err := engine.ExecuteQuery("network_logs", query)
		if err != nil {
			t.Fatalf("Query failed: %v", err)
		}

		// Count matching records in original data
		matchCount := 0
		for _, row := range rows {
			if row.Values["protocol"] == "TCP" && row.Values["action"] == "Block" {
				matchCount++
			}
		}

		if result.TotalMatches != matchCount {
			t.Errorf("Complex query count mismatch: query=%d, actual=%d", result.TotalMatches, matchCount)
		}
		t.Logf("TCP+Block records: %d (verified)", matchCount)

		// Verify all returned records match criteria
		for _, row := range result.Rows {
			if row["protocol"] != "TCP" || row["action"] != "Block" {
				t.Errorf("Invalid result row: protocol=%s, action=%s", row["protocol"], row["action"])
			}
		}
	})
}
