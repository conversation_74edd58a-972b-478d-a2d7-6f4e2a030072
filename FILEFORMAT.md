# UltraFast Index File Format Specification

## 📋 **Overview**

The UltraFast Index (`.ufidx`) file format is designed for optimal performance with memory-mapped access, perfect hash tables, and cache-friendly data layouts.

## 🏗️ **File Structure**

```
┌─────────────────────────────────────────────────────────────┐
│                    HEADER (64 bytes)                        │
├─────────────────────────────────────────────────────────────┤
│                 HASH TABLE (variable)                       │
├─────────────────────────────────────────────────────────────┤
│                 KEY DIRECTORY (variable)                    │
├─────────────────────────────────────────────────────────────┤
│                 DATA SECTION (variable)                     │
└─────────────────────────────────────────────────────────────┘
```

## 📊 **Header Format (64 bytes)**

| Offset | Size | Field | Description |
|--------|------|-------|-------------|
| 0-7    | 8    | Magic | "ULTRAFAS" (magic number) |
| 8-11   | 4    | NumKeys | Number of unique keys (big-endian) |
| 12-15  | 4    | HeaderSize | Size of header (64) |
| 16-19  | 4    | MaxKeysPerNode | B+ tree parameter (unused) |
| 20-23  | 4    | NodeSize | Page size (4096) |
| 24-27  | 4    | HashTableSize | Number of hash table entries |
| 28-31  | 4    | KeySlotSize | Size of each key slot (32) |
| 32-35  | 4    | Version | File format version (1) |
| 36-39  | 4    | Flags | Feature flags (0) |
| 40-43  | 4    | Checksum | Header checksum (CRC32) |
| 44-47  | 4    | Reserved1 | Reserved for future use |
| 48-51  | 4    | Reserved2 | Reserved for future use |
| 52-55  | 4    | Reserved3 | Reserved for future use |
| 56-59  | 4    | Reserved4 | Reserved for future use |
| 60-63  | 4    | Reserved5 | Reserved for future use |

### **Magic Number:**
- **Value**: "ULTRAFAS" (8 bytes)
- **Purpose**: File format identification
- **Validation**: Must match exactly for valid file

### **Hash Table Size:**
- **Calculation**: `nextPowerOf2(numKeys * 2)`
- **Load Factor**: 0.5 (50% occupancy)
- **Collision Resolution**: Linear probing

## 🔗 **Hash Table Section**

Located immediately after header at offset 64.

### **Structure:**
```
Entry[0]: KeyIndex (4 bytes, big-endian)
Entry[1]: KeyIndex (4 bytes, big-endian)
...
Entry[HashTableSize-1]: KeyIndex (4 bytes, big-endian)
```

### **Key Index Values:**
- **0**: Empty slot (no key)
- **1-N**: 1-based index into key directory
- **Collision Handling**: Linear probing to next slot

### **Hash Function:**
```go
func fnvHash32(s string) uint32 {
    const (
        fnvPrime  = 16777619
        fnvOffset = 2166136261
    )
    hash := uint32(fnvOffset)
    for _, b := range []byte(s) {
        hash ^= uint32(b)
        hash *= fnvPrime
    }
    return hash
}

// Usage:
slot := fnvHash32(key) % hashTableSize
```

## 📁 **Key Directory Section**

Located after hash table at offset: `64 + (HashTableSize * 4)`

### **Structure:**
Each key entry is exactly 32 bytes (KeySlotSize):

| Offset | Size | Field | Description |
|--------|------|-------|-------------|
| 0-23   | 24   | KeyData | Null-terminated key string |
| 24-27  | 4    | DataOffset | Offset to line numbers array |
| 28-31  | 4    | Count | Number of line numbers |

### **Key Data:**
- **Size**: 24 bytes maximum
- **Encoding**: UTF-8 string
- **Termination**: Null-terminated
- **Padding**: Remaining bytes are zero

### **Data Offset:**
- **Type**: uint32 (big-endian)
- **Points to**: Start of line numbers array in data section
- **Absolute**: Offset from beginning of file

### **Count:**
- **Type**: uint32 (big-endian)
- **Value**: Number of line numbers for this key
- **Range**: 1 to N (never zero for valid entries)

## 📊 **Data Section**

Located after key directory. Contains arrays of line numbers for each key.

### **Structure:**
```
LineNumberArray[0]:
  Count (4 bytes, big-endian)
  LineNumber[0] (4 bytes, big-endian)
  LineNumber[1] (4 bytes, big-endian)
  ...
  LineNumber[Count-1] (4 bytes, big-endian)

LineNumberArray[1]:
  Count (4 bytes, big-endian)
  LineNumber[0] (4 bytes, big-endian)
  ...
```

### **Line Number Array:**
- **Count**: Redundant count field (matches key directory)
- **Line Numbers**: 1-based line numbers from original data
- **Encoding**: uint32 big-endian
- **Order**: Ascending order (sorted)

## 🔍 **Search Algorithm**

### **Lookup Process:**
1. **Hash Calculation**: `hash = fnvHash32(searchKey) % hashTableSize`
2. **Linear Probing**: Check slots starting at `hash`
3. **Key Comparison**: Compare with stored key data
4. **Data Retrieval**: Read line numbers from data section

### **Pseudocode:**
```go
func Search(key string) []uint32 {
    hash := fnvHash32(key) % hashTableSize
    
    for {
        keyIndex := hashTable[hash]
        if keyIndex == 0 {
            return nil // Not found
        }
        
        keyEntry := keyDirectory[keyIndex-1]
        if keyEntry.Key == key {
            // Found! Read line numbers
            return readLineNumbers(keyEntry.DataOffset, keyEntry.Count)
        }
        
        hash = (hash + 1) % hashTableSize // Linear probing
    }
}
```

## 🎯 **Performance Characteristics**

### **Time Complexity:**
- **Average Case**: O(1) - Direct hash lookup
- **Worst Case**: O(n) - All keys hash to same slot
- **Typical**: O(1.5) - With 50% load factor

### **Space Complexity:**
- **Hash Table**: `HashTableSize * 4` bytes
- **Key Directory**: `NumKeys * 32` bytes
- **Data Section**: `Sum(Count[i] * 4)` bytes
- **Total Overhead**: ~2x for hash table + fixed key slots

### **Cache Performance:**
- **Header**: Single cache line (64 bytes)
- **Hash Table**: Sequential access, cache-friendly
- **Key Directory**: Fixed-size entries, predictable access
- **Data Section**: Minimal seeks, good locality

## 🔧 **Optimization Details**

### **Memory Alignment:**
- **Header**: 64-byte aligned (cache line)
- **Hash Table**: 4-byte aligned entries
- **Key Directory**: 32-byte aligned entries
- **Data Section**: 4-byte aligned arrays

### **Endianness:**
- **All multi-byte values**: Big-endian
- **Reason**: Network byte order, consistent across platforms
- **Conversion**: Use `binary.BigEndian` in Go

### **String Comparison:**
- **SIMD Optimization**: 8-byte parallel comparison
- **Unsafe Pointers**: Direct memory access
- **Early Termination**: Stop at first difference

## 🛠️ **File Validation**

### **Header Validation:**
```go
func ValidateHeader(header []byte) error {
    if string(header[0:8]) != "ULTRAFAS" {
        return errors.New("invalid magic number")
    }
    
    if binary.BigEndian.Uint32(header[12:16]) != 64 {
        return errors.New("invalid header size")
    }
    
    // Validate checksum
    expectedChecksum := binary.BigEndian.Uint32(header[40:44])
    actualChecksum := crc32.ChecksumIEEE(header[0:40])
    if expectedChecksum != actualChecksum {
        return errors.New("header checksum mismatch")
    }
    
    return nil
}
```

### **Integrity Checks:**
1. **Magic Number**: Must be "ULTRAFAS"
2. **Header Size**: Must be 64
3. **Hash Table Size**: Must be power of 2
4. **Key Count**: Must match actual entries
5. **Data Offsets**: Must be within file bounds
6. **Line Number Counts**: Must match between directory and data

## 📏 **Size Calculations**

### **File Size Formula:**
```
FileSize = HeaderSize + HashTableSize + KeyDirectorySize + DataSectionSize

Where:
- HeaderSize = 64
- HashTableSize = HashTableEntries * 4
- KeyDirectorySize = NumKeys * 32
- DataSectionSize = Sum(4 + Count[i] * 4) for all keys
```

### **Memory Usage:**
```
MemoryUsage = FileSize (when memory-mapped)
             + HashTableCache (optional)
             + QueryEngine overhead (~1KB)
```

## 🔄 **Version History**

### **Version 1 (Current):**
- Initial implementation
- Perfect hash tables with linear probing
- Fixed 32-byte key slots
- Big-endian encoding
- Memory-mapped access optimized

### **Future Versions:**
- **Version 2**: Compressed key storage
- **Version 3**: Multi-level hash tables
- **Version 4**: SIMD-optimized hash functions
