#!/bin/bash

# UltraFast Individual Query Performance Tester
# This script allows you to test individual query performance with microsecond precision

echo "🚀 UltraFast Individual Query Performance Tester"
echo "================================================"
echo

# Function to measure individual query performance
measure_individual_query() {
    local table="$1"
    local filter="$2"
    local columns="$3"
    local iterations="${4:-20}"
    
    echo "🔍 Testing Query Performance"
    echo "Table: $table"
    echo "Filter: $filter"
    echo "Columns: $columns"
    echo "Iterations: $iterations"
    echo

    # Check if indexes exist
    if [ ! -d "./demo_indexes" ]; then
        echo "❌ Index directory not found. Please run demo.sh first to generate indexes."
        exit 1
    fi

    # Build if needed
    if [ ! -f "./ultrafast" ]; then
        echo "🔧 Building UltraFast..."
        go build -o ultrafast .
    fi

    # Warm up (important for accurate measurements)
    echo "🔥 Warming up..."
    for i in {1..5}; do
        ./ultrafast query ./demo_indexes "$table" "$filter" "$columns" > /dev/null 2>&1
    done

    # Measure performance
    echo "📊 Measuring performance over $iterations iterations..."
    local total_time=0
    local min_time=999999999
    local max_time=0
    local times=()

    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s%N)
        ./ultrafast query ./demo_indexes "$table" "$filter" "$columns" > /dev/null 2>&1
        local end_time=$(date +%s%N)
        local duration=$((end_time - start_time))
        local duration_us=$((duration / 1000))
        
        times+=($duration_us)
        total_time=$((total_time + duration_us))
        
        if [ $duration_us -lt $min_time ]; then
            min_time=$duration_us
        fi
        
        if [ $duration_us -gt $max_time ]; then
            max_time=$duration_us
        fi
        
        # Show progress
        if [ $((i % 5)) -eq 0 ]; then
            echo "  Completed $i/$iterations iterations..."
        fi
    done

    local avg_time=$((total_time / iterations))

    # Calculate median
    IFS=$'\n' sorted_times=($(sort -n <<<"${times[*]}"))
    local median_time
    if [ $((iterations % 2)) -eq 0 ]; then
        local mid1=$((iterations / 2 - 1))
        local mid2=$((iterations / 2))
        median_time=$(((sorted_times[mid1] + sorted_times[mid2]) / 2))
    else
        local mid=$((iterations / 2))
        median_time=${sorted_times[mid]}
    fi

    echo
    echo "📈 Performance Results:"
    echo "  Average:  ${avg_time}µs"
    echo "  Median:   ${median_time}µs"
    echo "  Minimum:  ${min_time}µs"
    echo "  Maximum:  ${max_time}µs"
    echo "  Range:    $((max_time - min_time))µs"
    
    # Performance assessment
    echo
    echo "🎯 Performance Assessment:"
    if [ $avg_time -lt 1000 ]; then
        echo "  ✅ EXCELLENT: Sub-microsecond performance!"
    elif [ $avg_time -lt 5000 ]; then
        echo "  ✅ OUTSTANDING: Microsecond-level performance achieved!"
    elif [ $avg_time -lt 10000 ]; then
        echo "  ✅ VERY GOOD: Single-digit microsecond performance"
    elif [ $avg_time -lt 50000 ]; then
        echo "  ⚠️  GOOD: Competitive with BigQuery (5-50µs)"
    elif [ $avg_time -lt 100000 ]; then
        echo "  ⚠️  ACCEPTABLE: Still much faster than traditional databases"
    else
        echo "  ❌ SLOW: May need optimization"
    fi

    # Show actual results
    echo
    echo "📋 Query Results:"
    ./ultrafast query ./demo_indexes "$table" "$filter" "$columns"
    echo
}

# Check command line arguments
if [ $# -eq 0 ]; then
    echo "Usage: $0 <table> <filter> [columns] [iterations]"
    echo
    echo "Examples:"
    echo "  $0 users \"department=IT\" \"username,email\""
    echo "  $0 users \"status=active AND role=admin\" \"username,department,role\" 50"
    echo "  $0 logs \"action=login\" \"user_id,timestamp\""
    echo
    echo "Available tables (after running demo.sh):"
    echo "  • users   - User information"
    echo "  • logs    - Activity logs"
    echo "  • mock_data - Real mock data (if mock_data.csv exists)"
    echo
    echo "🚀 Quick Start:"
    echo "1. Run './demo.sh' first to generate sample data and indexes"
    echo "2. Then use this script to test individual query performance"
    echo
    exit 1
fi

# Parse arguments
TABLE="$1"
FILTER="$2"
COLUMNS="${3:-*}"
ITERATIONS="${4:-20}"

# Validate iterations
if ! [[ "$ITERATIONS" =~ ^[0-9]+$ ]] || [ "$ITERATIONS" -lt 1 ]; then
    echo "❌ Invalid iterations count. Must be a positive integer."
    exit 1
fi

# Run the performance test
measure_individual_query "$TABLE" "$FILTER" "$COLUMNS" "$ITERATIONS"

echo "💡 Tips for Performance Testing:"
echo "  • Use more iterations (50-100) for more accurate results"
echo "  • Test different query types (simple, complex, negative)"
echo "  • Compare performance with different result set sizes"
echo "  • Warm-up is automatically handled for accurate measurements"
echo
echo "🔧 Advanced Testing:"
echo "  • Run 'go test -bench=.' for comprehensive benchmarks"
echo "  • Use 'go test -run TestMockDataV2MicrosecondPerformance' for detailed analysis"
echo "  • Check 'mock_data_v2_performance_test.go' for real-world performance tests"
